// Example Database Queries for Neon PostgreSQL
// This file demonstrates how to query your database using the existing infrastructure

import { count, desc, eq, sql } from "drizzle-orm";
import { createDbFromEnv } from "./app/lib/db/db.js";
import { dbOperations } from "./app/lib/db/operations.js";
import * as schema from "./app/lib/db/schema.js";

// Initialize database connection
// Make sure you have DATABASE_URL in your .dev.vars file
const db = createDbFromEnv(process.env);

// Example 1: Get all users with their credit balances
async function getAllUsersWithCredits() {
  try {
    const users = await db
      .select({
        id: schema.users.id,
        name: schema.users.name,
        email: schema.users.email,
        credits: schema.users.credits,
        createdAt: schema.users.createdAt,
      })
      .from(schema.users)
      .orderBy(desc(schema.users.createdAt));

    console.log("Users with credits:", users);
    return users;
  } catch (error) {
    console.error("Error fetching users:", error);
  }
}

// Example 2: Get database statistics
async function getDatabaseStats() {
  try {
    const stats = await dbOperations.health.getStats(db);
    console.log("Database Statistics:", stats);
    return stats;
  } catch (error) {
    console.error("Error getting stats:", error);
  }
}

// Example 3: Get user orders with pagination
async function getUserOrders(userUuid, page = 1, limit = 10) {
  try {
    const orders = await dbOperations.order.getUserOrders(db, userUuid, {
      page,
      limit,
      sortOrder: "desc",
    });

    console.log("User orders:", orders);
    return orders;
  } catch (error) {
    console.error("Error fetching orders:", error);
  }
}

// Example 4: Get top users by credit usage
async function getTopUsersByCredits() {
  try {
    const topUsers = await db
      .select({
        name: schema.users.name,
        email: schema.users.email,
        credits: schema.users.credits,
        totalTransactions: count(schema.creditTransactions.id),
      })
      .from(schema.users)
      .leftJoin(
        schema.creditTransactions,
        eq(schema.users.uuid, schema.creditTransactions.userUuid)
      )
      .groupBy(schema.users.id, schema.users.name, schema.users.email, schema.users.credits)
      .orderBy(desc(schema.users.credits))
      .limit(10);

    console.log("Top users by credits:", topUsers);
    return topUsers;
  } catch (error) {
    console.error("Error fetching top users:", error);
  }
}

// Example 5: Get recent API usage
async function getRecentApiUsage(limit = 20) {
  try {
    const apiUsage = await db
      .select({
        endpoint: schema.apiUsage.endpoint,
        provider: schema.apiUsage.provider,
        model: schema.apiUsage.model,
        tokensUsed: schema.apiUsage.tokensUsed,
        creditsUsed: schema.apiUsage.creditsUsed,
        status: schema.apiUsage.status,
        createdAt: schema.apiUsage.createdAt,
      })
      .from(schema.apiUsage)
      .orderBy(desc(schema.apiUsage.createdAt))
      .limit(limit);

    console.log("Recent API usage:", apiUsage);
    return apiUsage;
  } catch (error) {
    console.error("Error fetching API usage:", error);
  }
}

// Example 6: Get conversations with message counts
async function getConversationsWithMessageCounts() {
  try {
    const conversations = await db
      .select({
        id: schema.conversations.id,
        title: schema.conversations.title,
        model: schema.conversations.model,
        provider: schema.conversations.provider,
        messageCount: count(schema.messages.id),
        lastMessageAt: schema.conversations.lastMessageAt,
        createdAt: schema.conversations.createdAt,
      })
      .from(schema.conversations)
      .leftJoin(schema.messages, eq(schema.conversations.id, schema.messages.conversationId))
      .groupBy(
        schema.conversations.id,
        schema.conversations.title,
        schema.conversations.model,
        schema.conversations.provider,
        schema.conversations.lastMessageAt,
        schema.conversations.createdAt
      )
      .orderBy(desc(schema.conversations.lastMessageAt))
      .limit(20);

    console.log("Conversations with message counts:", conversations);
    return conversations;
  } catch (error) {
    console.error("Error fetching conversations:", error);
  }
}

// Example 7: Get monthly revenue from orders
async function getMonthlyRevenue() {
  try {
    const monthlyRevenue = await db
      .select({
        month: sql`DATE_TRUNC('month', ${schema.orders.createdAt})`,
        totalRevenue: sql`SUM(${schema.orders.totalAmount})`,
        orderCount: count(schema.orders.id),
      })
      .from(schema.orders)
      .where(eq(schema.orders.status, "succeeded"))
      .groupBy(sql`DATE_TRUNC('month', ${schema.orders.createdAt})`)
      .orderBy(sql`DATE_TRUNC('month', ${schema.orders.createdAt}) DESC`)
      .limit(12);

    console.log("Monthly revenue:", monthlyRevenue);
    return monthlyRevenue;
  } catch (error) {
    console.error("Error fetching monthly revenue:", error);
  }
}

// Example 8: Health check
async function checkDatabaseHealth() {
  try {
    const health = await db.healthCheck();
    console.log("Database health:", health);
    return health;
  } catch (error) {
    console.error("Error checking database health:", error);
  }
}

// Export functions for use
export {
  getAllUsersWithCredits,
  getDatabaseStats,
  getUserOrders,
  getTopUsersByCredits,
  getRecentApiUsage,
  getConversationsWithMessageCounts,
  getMonthlyRevenue,
  checkDatabaseHealth,
};

// Example usage:
// Run these functions to query your database
async function runExamples() {
  console.log("=== Database Query Examples ===\n");

  // Health check first
  await checkDatabaseHealth();

  // Get basic stats
  await getDatabaseStats();

  // Get users
  await getAllUsersWithCredits();

  // Get top users by credits
  await getTopUsersByCredits();

  // Get recent API usage
  await getRecentApiUsage();

  // Get conversations
  await getConversationsWithMessageCounts();

  // Get revenue data
  await getMonthlyRevenue();
}

// Uncomment to run examples
// runExamples().catch(console.error);
