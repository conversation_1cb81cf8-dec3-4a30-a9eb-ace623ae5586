/**
 * Enhanced Feedback API
 * Handles feedback creation, retrieval, and management
 */

import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";
import { respData, respErr } from "~/lib/api/resp";
import { getUser } from "~/lib/auth/middleware.server";
import { createDbFromEnv } from "~/lib/db";
import {
  createFeedback,
  deleteFeedback,
  type FeedbackPriority,
  type FeedbackStatus,
  type FeedbackType,
  getFeedback,
  getFeedbackById,
  getFeedbackStats,
  updateFeedback,
} from "~/services/feedback.server";

// Input validation schema
interface FeedbackInput {
  title?: string;
  content?: string;
  type?: FeedbackType;
  priority?: FeedbackPriority;
  rating?: number;
  userEmail?: string;
  userName?: string;
  tags?: string[];
  metadata?: Record<string, any>;
}

interface UpdateFeedbackInput {
  title?: string;
  content?: string;
  type?: FeedbackType;
  priority?: FeedbackPriority;
  status?: FeedbackStatus;
  rating?: number;
  tags?: string[];
  adminNotes?: string;
  assignedTo?: string;
}

export async function loader({ request, context }: LoaderFunctionArgs) {
  try {
    const db = context.db || createDbFromEnv(context.cloudflare?.env);

    const url = new URL(request.url);
    const action = url.searchParams.get("action");
    const feedbackId = url.searchParams.get("id");

    switch (action) {
      case "list": {
        const page = parseInt(url.searchParams.get("page") || "1", 10);
        const limit = parseInt(url.searchParams.get("limit") || "20", 10);
        const status = url.searchParams.get("status") as FeedbackStatus | null;
        const type = url.searchParams.get("type") as FeedbackType | null;
        const priority = url.searchParams.get("priority") as FeedbackPriority | null;
        const search = url.searchParams.get("search") || undefined;
        const userUuid = url.searchParams.get("user_uuid") || undefined;

        const result = await getFeedback(
          {
            page,
            limit,
            status: status || undefined,
            type: type || undefined,
            priority: priority || undefined,
            search,
            userUuid,
          },
          db
        );

        return respData(result);
      }

      case "get": {
        if (!feedbackId) {
          return respErr("Feedback ID is required");
        }

        const feedback = await getFeedbackById(parseInt(feedbackId, 10), db);
        if (!feedback) {
          return respErr("Feedback not found");
        }

        return respData({ feedback });
      }

      case "stats": {
        const stats = await getFeedbackStats(db);
        return respData({ stats });
      }

      default:
        return respErr("Invalid action. Supported actions: list, get, stats");
    }
  } catch (error) {
    console.error("Error in feedback API loader:", error);
    return respErr(error instanceof Error ? error.message : "Failed to process request");
  }
}

export async function action({ request, context }: ActionFunctionArgs) {
  try {
    const db = context.db || createDbFromEnv(context.cloudflare?.env);

    const body = await request.json();
    const { action } = body;

    switch (action) {
      case "create": {
        const { title, content, type, priority, rating, userEmail, userName, tags, metadata } =
          body as FeedbackInput;

        // Get user if authenticated
        const userResult = await getUser(request);
        const user = userResult.success ? userResult.user : null;

        // Validate required fields
        if (!title) {
          return respErr("Title is required");
        }

        if (!content) {
          return respErr("Content is required");
        }

        // For anonymous feedback, require email
        if (!user && !userEmail) {
          return respErr("Email is required for anonymous feedback");
        }

        const result = await createFeedback(
          {
            userUuid: user?.id,
            userEmail: userEmail || user?.email,
            userName: userName || user?.name,
            title,
            content,
            type: type || "general_feedback",
            priority: priority || "medium",
            rating,
            tags,
            metadata,
          },
          db
        );

        if (result.success) {
          return respData({
            message: "Feedback submitted successfully",
            feedback: result.feedback,
          });
        } else {
          return respErr(result.error || "Failed to create feedback");
        }
      }

      case "update": {
        const { feedbackId, ...updateData } = body as UpdateFeedbackInput & { feedbackId: number };

        if (!feedbackId) {
          return respErr("Feedback ID is required");
        }

        // TODO: Add admin authentication check

        const result = await updateFeedback(feedbackId, updateData, db);

        if (result.success) {
          return respData({ message: "Feedback updated successfully" });
        } else {
          return respErr(result.error || "Failed to update feedback");
        }
      }

      case "delete": {
        const { feedbackId } = body;

        if (!feedbackId) {
          return respErr("Feedback ID is required");
        }

        // TODO: Add admin authentication check

        const result = await deleteFeedback(feedbackId, db);

        if (result.success) {
          return respData({ message: "Feedback deleted successfully" });
        } else {
          return respErr(result.error || "Failed to delete feedback");
        }
      }

      default:
        return respErr("Invalid action. Supported actions: create, update, delete");
    }
  } catch (error) {
    console.error("Error in feedback API action:", error);
    return respErr(error instanceof Error ? error.message : "Failed to process request");
  }
}
