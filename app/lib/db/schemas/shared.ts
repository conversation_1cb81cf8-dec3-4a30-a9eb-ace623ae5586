// Shared enums and types used across multiple schemas
import { pgEnum } from "drizzle-orm/pg-core";

// Notification enums
export const notificationChannelEnum = pgEnum("notification_channel", ["in_app", "email"]);
export const notificationTypeEnum = pgEnum("notification_type", [
  "info",
  "warning",
  "error",
  "success",
  "payment",
  "credit",
  "usage",
  "system",
  "security",
]);

// App permissions enum
export const appPermissionsEnum = pgEnum("app_permissions", [
  "roles.manage",
  "billing.manage",
  "notifications.manage",
  "users.manage",
  "orders.manage",
  "feedback.manage",
  "analytics.view",
  "api_keys.manage",
  "content.manage",
  "system.manage",
]);

// Common utility types
export type TimestampFields = {
  createdAt: Date;
  updatedAt: Date;
};

export type OptionalTimestampFields = {
  createdAt?: Date;
  updatedAt?: Date;
};
