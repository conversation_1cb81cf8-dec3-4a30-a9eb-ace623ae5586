/**
 * System Monitoring Service
 * Comprehensive system monitoring with real-time metrics, alerts, and health checks
 */

export interface SystemMetrics {
  timestamp: Date;
  system: {
    uptime: number;
    memory: MemoryMetrics;
    cpu?: CpuMetrics;
    disk?: DiskMetrics;
  };
  application: {
    responseTime: number;
    requestCount: number;
    errorRate: number;
    activeConnections: number;
  };
  database: {
    connectionCount: number;
    queryTime: number;
    slowQueries: number;
    connectionErrors: number;
  };
  cache: {
    hitRate: number;
    memoryUsage: number;
    entryCount: number;
    evictions: number;
  };
  external: {
    aiProviders: Record<string, ProviderStatus>;
    thirdPartyServices: Record<string, ServiceStatus>;
  };
}

export interface MemoryMetrics {
  used: number;
  total: number;
  percentage: number;
  heapUsed?: number;
  heapTotal?: number;
}

export interface CpuMetrics {
  usage: number;
  loadAverage?: number[];
}

export interface DiskMetrics {
  used: number;
  total: number;
  percentage: number;
}

export interface ProviderStatus {
  status: "healthy" | "degraded" | "unhealthy";
  responseTime: number;
  errorRate: number;
  lastCheck: Date;
}

export interface ServiceStatus {
  status: "healthy" | "degraded" | "unhealthy";
  responseTime: number;
  lastCheck: Date;
  error?: string;
}

export interface Alert {
  id: string;
  type: "critical" | "warning" | "info";
  title: string;
  message: string;
  timestamp: Date;
  resolved: boolean;
  resolvedAt?: Date;
  metadata?: Record<string, any>;
}

export interface MonitoringConfig {
  enabled: boolean;
  interval: number; // Monitoring interval in milliseconds
  alertThresholds: {
    memoryUsage: number; // Percentage
    responseTime: number; // Milliseconds
    errorRate: number; // Percentage
    diskUsage: number; // Percentage
  };
  retentionPeriod: number; // Days to keep metrics
  alertCooldown: number; // Milliseconds between same alert types
}

// Default monitoring configuration
export const DEFAULT_MONITORING_CONFIG: MonitoringConfig = {
  enabled: true,
  interval: 30000, // 30 seconds
  alertThresholds: {
    memoryUsage: 85, // 85%
    responseTime: 2000, // 2 seconds
    errorRate: 5, // 5%
    diskUsage: 90, // 90%
  },
  retentionPeriod: 7, // 7 days
  alertCooldown: 300000, // 5 minutes
};

// In-memory storage for metrics and alerts
const systemMetrics: SystemMetrics[] = [];
const activeAlerts: Alert[] = [];
const alertHistory: Alert[] = [];
const maxMetricsHistory = 2880; // 24 hours at 30-second intervals

let monitoringInterval: NodeJS.Timeout | null = null;
const lastAlertTimes: Record<string, number> = {};

/**
 * Initialize system monitoring
 */
export function initSystemMonitoring(config: Partial<MonitoringConfig> = {}): void {
  const finalConfig = { ...DEFAULT_MONITORING_CONFIG, ...config };

  if (!finalConfig.enabled) {
    console.log("[MONITOR] System monitoring disabled");
    return;
  }

  // Stop existing monitoring
  if (monitoringInterval) {
    clearInterval(monitoringInterval);
  }

  // Start monitoring interval
  monitoringInterval = setInterval(async () => {
    try {
      await collectSystemMetrics();
      await checkAlertConditions(finalConfig);
    } catch (error) {
      console.error("[MONITOR] Error during monitoring cycle:", error);
    }
  }, finalConfig.interval);

  console.log(`[MONITOR] System monitoring started with ${finalConfig.interval}ms interval`);
}

/**
 * Collect current system metrics
 */
export async function collectSystemMetrics(): Promise<SystemMetrics> {
  const timestamp = new Date();

  const metrics: SystemMetrics = {
    timestamp,
    system: {
      uptime: getUptime(),
      memory: getMemoryMetrics(),
      cpu: getCpuMetrics(),
      disk: getDiskMetrics(),
    },
    application: {
      responseTime: getAverageResponseTime(),
      requestCount: getRequestCount(),
      errorRate: getErrorRate(),
      activeConnections: getActiveConnections(),
    },
    database: {
      connectionCount: getDatabaseConnections(),
      queryTime: getAverageQueryTime(),
      slowQueries: getSlowQueryCount(),
      connectionErrors: getDatabaseErrors(),
    },
    cache: {
      hitRate: getCacheHitRate(),
      memoryUsage: getCacheMemoryUsage(),
      entryCount: getCacheEntryCount(),
      evictions: getCacheEvictions(),
    },
    external: {
      aiProviders: await checkAiProviders(),
      thirdPartyServices: await checkThirdPartyServices(),
    },
  };

  // Store metrics
  systemMetrics.push(metrics);

  // Keep only recent metrics
  if (systemMetrics.length > maxMetricsHistory) {
    systemMetrics.splice(0, systemMetrics.length - maxMetricsHistory);
  }

  return metrics;
}

/**
 * Get system uptime
 */
function getUptime(): number {
  if (typeof process !== "undefined" && process.uptime) {
    return process.uptime();
  }
  return 0;
}

/**
 * Get memory metrics
 */
function getMemoryMetrics(): MemoryMetrics {
  if (typeof process !== "undefined" && process.memoryUsage) {
    const usage = process.memoryUsage();
    return {
      used: usage.heapUsed,
      total: usage.heapTotal,
      percentage: (usage.heapUsed / usage.heapTotal) * 100,
      heapUsed: usage.heapUsed,
      heapTotal: usage.heapTotal,
    };
  }

  // Fallback for browser/worker environment
  if (typeof performance !== "undefined" && (performance as any).memory) {
    const memory = (performance as any).memory;
    return {
      used: memory.usedJSHeapSize,
      total: memory.totalJSHeapSize,
      percentage: (memory.usedJSHeapSize / memory.totalJSHeapSize) * 100,
    };
  }

  return {
    used: 0,
    total: 0,
    percentage: 0,
  };
}

/**
 * Get CPU metrics (placeholder - limited in browser/worker environments)
 */
function getCpuMetrics(): CpuMetrics | undefined {
  // In a full Node.js environment, you would use os.cpus() and os.loadavg()
  // For Cloudflare Workers, CPU metrics are not directly available
  return undefined;
}

/**
 * Get disk metrics (placeholder - not available in browser/worker environments)
 */
function getDiskMetrics(): DiskMetrics | undefined {
  // In a full Node.js environment, you would use fs.statSync() or similar
  // For Cloudflare Workers, disk metrics are not applicable
  return undefined;
}

/**
 * Get average response time from recent requests
 */
function getAverageResponseTime(): number {
  // This would integrate with the performance monitoring system
  // For now, return a mock value
  return Math.random() * 500 + 100; // 100-600ms
}

/**
 * Get total request count
 */
function getRequestCount(): number {
  // This would integrate with the request tracking system
  return Math.floor(Math.random() * 10000);
}

/**
 * Get current error rate
 */
function getErrorRate(): number {
  // This would integrate with the error tracking system
  return Math.random() * 5; // 0-5%
}

/**
 * Get active connection count
 */
function getActiveConnections(): number {
  // This would integrate with the connection pool
  return Math.floor(Math.random() * 50);
}

/**
 * Get database connection count
 */
function getDatabaseConnections(): number {
  // This would integrate with the database connection pool
  return Math.floor(Math.random() * 10);
}

/**
 * Get average database query time
 */
function getAverageQueryTime(): number {
  // This would integrate with the database monitoring
  return Math.random() * 100 + 10; // 10-110ms
}

/**
 * Get slow query count
 */
function getSlowQueryCount(): number {
  // This would integrate with the database monitoring
  return Math.floor(Math.random() * 5);
}

/**
 * Get database error count
 */
function getDatabaseErrors(): number {
  // This would integrate with the database monitoring
  return Math.floor(Math.random() * 3);
}

/**
 * Get cache hit rate
 */
function getCacheHitRate(): number {
  // This would integrate with the cache manager
  return Math.random() * 30 + 70; // 70-100%
}

/**
 * Get cache memory usage
 */
function getCacheMemoryUsage(): number {
  // This would integrate with the cache manager
  return Math.random() * 100 * 1024 * 1024; // 0-100MB
}

/**
 * Get cache entry count
 */
function getCacheEntryCount(): number {
  // This would integrate with the cache manager
  return Math.floor(Math.random() * 10000);
}

/**
 * Get cache eviction count
 */
function getCacheEvictions(): number {
  // This would integrate with the cache manager
  return Math.floor(Math.random() * 100);
}

/**
 * Check AI provider status
 */
async function checkAiProviders(): Promise<Record<string, ProviderStatus>> {
  const providers = ["openai", "anthropic", "cloudflare"];
  const statuses: Record<string, ProviderStatus> = {};

  for (const provider of providers) {
    // Mock provider check - in production, this would make actual health check requests
    const responseTime = Math.random() * 2000 + 500; // 500-2500ms
    const errorRate = Math.random() * 10; // 0-10%

    statuses[provider] = {
      status: errorRate > 5 ? "degraded" : responseTime > 2000 ? "degraded" : "healthy",
      responseTime,
      errorRate,
      lastCheck: new Date(),
    };
  }

  return statuses;
}

/**
 * Check third-party service status
 */
async function checkThirdPartyServices(): Promise<Record<string, ServiceStatus>> {
  const services = ["stripe", "sendgrid", "cloudflare"];
  const statuses: Record<string, ServiceStatus> = {};

  for (const service of services) {
    // Mock service check - in production, this would make actual health check requests
    const responseTime = Math.random() * 1000 + 200; // 200-1200ms
    const isHealthy = Math.random() > 0.1; // 90% healthy

    statuses[service] = {
      status: isHealthy ? "healthy" : "unhealthy",
      responseTime,
      lastCheck: new Date(),
      error: isHealthy ? undefined : "Service unavailable",
    };
  }

  return statuses;
}

/**
 * Check alert conditions and trigger alerts
 */
async function checkAlertConditions(config: MonitoringConfig): Promise<void> {
  if (systemMetrics.length === 0) return;

  const latest = systemMetrics[systemMetrics.length - 1];
  const now = Date.now();

  // Check memory usage
  if (latest.system.memory.percentage > config.alertThresholds.memoryUsage) {
    await triggerAlert(
      {
        type: "critical",
        title: "High Memory Usage",
        message: `Memory usage is ${latest.system.memory.percentage.toFixed(1)}% (threshold: ${config.alertThresholds.memoryUsage}%)`,
        metadata: { memoryUsage: latest.system.memory.percentage },
      },
      config.alertCooldown
    );
  }

  // Check response time
  if (latest.application.responseTime > config.alertThresholds.responseTime) {
    await triggerAlert(
      {
        type: "warning",
        title: "High Response Time",
        message: `Average response time is ${latest.application.responseTime}ms (threshold: ${config.alertThresholds.responseTime}ms)`,
        metadata: { responseTime: latest.application.responseTime },
      },
      config.alertCooldown
    );
  }

  // Check error rate
  if (latest.application.errorRate > config.alertThresholds.errorRate) {
    await triggerAlert(
      {
        type: "critical",
        title: "High Error Rate",
        message: `Error rate is ${latest.application.errorRate.toFixed(1)}% (threshold: ${config.alertThresholds.errorRate}%)`,
        metadata: { errorRate: latest.application.errorRate },
      },
      config.alertCooldown
    );
  }

  // Check external services
  for (const [provider, status] of Object.entries(latest.external.aiProviders)) {
    if (status.status === "unhealthy") {
      await triggerAlert(
        {
          type: "critical",
          title: "AI Provider Unhealthy",
          message: `AI provider ${provider} is unhealthy (error rate: ${status.errorRate.toFixed(1)}%)`,
          metadata: { provider, status },
        },
        config.alertCooldown
      );
    }
  }
}

/**
 * Trigger an alert with cooldown
 */
async function triggerAlert(
  alertData: Omit<Alert, "id" | "timestamp" | "resolved">,
  cooldown: number
): Promise<void> {
  const alertKey = `${alertData.type}:${alertData.title}`;
  const now = Date.now();

  // Check cooldown
  if (lastAlertTimes[alertKey] && now - lastAlertTimes[alertKey] < cooldown) {
    return;
  }

  const alert: Alert = {
    id: generateAlertId(),
    timestamp: new Date(),
    resolved: false,
    ...alertData,
  };

  activeAlerts.push(alert);
  alertHistory.push(alert);
  lastAlertTimes[alertKey] = now;

  // Log alert
  console.warn(`[MONITOR] ALERT [${alert.type.toUpperCase()}] ${alert.title}: ${alert.message}`);

  // TODO: Send to external alerting system (email, Slack, etc.)
}

/**
 * Generate unique alert ID
 */
function generateAlertId(): string {
  return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Get current system metrics
 */
export function getCurrentMetrics(): SystemMetrics | null {
  return systemMetrics.length > 0 ? systemMetrics[systemMetrics.length - 1] : null;
}

/**
 * Get metrics history
 */
export function getMetricsHistory(limit?: number): SystemMetrics[] {
  const metrics = [...systemMetrics];
  return limit ? metrics.slice(-limit) : metrics;
}

/**
 * Get active alerts
 */
export function getActiveAlerts(): Alert[] {
  return activeAlerts.filter((alert) => !alert.resolved);
}

/**
 * Get alert history
 */
export function getAlertHistory(limit?: number): Alert[] {
  const alerts = [...alertHistory];
  return limit ? alerts.slice(-limit) : alerts;
}

/**
 * Resolve an alert
 */
export function resolveAlert(alertId: string): boolean {
  const alert = activeAlerts.find((a) => a.id === alertId);
  if (alert) {
    alert.resolved = true;
    alert.resolvedAt = new Date();
    return true;
  }
  return false;
}

/**
 * Get system health summary
 */
export function getSystemHealth(): {
  status: "healthy" | "degraded" | "unhealthy";
  score: number;
  issues: string[];
} {
  const current = getCurrentMetrics();
  if (!current) {
    return { status: "unhealthy", score: 0, issues: ["No metrics available"] };
  }

  const issues: string[] = [];
  let score = 100;

  // Check memory
  if (current.system.memory.percentage > 90) {
    issues.push("High memory usage");
    score -= 30;
  } else if (current.system.memory.percentage > 80) {
    issues.push("Elevated memory usage");
    score -= 15;
  }

  // Check response time
  if (current.application.responseTime > 2000) {
    issues.push("High response time");
    score -= 25;
  } else if (current.application.responseTime > 1000) {
    issues.push("Elevated response time");
    score -= 10;
  }

  // Check error rate
  if (current.application.errorRate > 5) {
    issues.push("High error rate");
    score -= 35;
  } else if (current.application.errorRate > 2) {
    issues.push("Elevated error rate");
    score -= 15;
  }

  // Check external services
  const unhealthyProviders = Object.entries(current.external.aiProviders)
    .filter(([, status]) => status.status === "unhealthy")
    .map(([name]) => name);

  if (unhealthyProviders.length > 0) {
    issues.push(`Unhealthy AI providers: ${unhealthyProviders.join(", ")}`);
    score -= 20 * unhealthyProviders.length;
  }

  score = Math.max(0, score);

  let status: "healthy" | "degraded" | "unhealthy";
  if (score >= 80) {
    status = "healthy";
  } else if (score >= 50) {
    status = "degraded";
  } else {
    status = "unhealthy";
  }

  return { status, score, issues };
}

/**
 * Shutdown system monitoring
 */
export function shutdownSystemMonitoring(): void {
  if (monitoringInterval) {
    clearInterval(monitoringInterval);
    monitoringInterval = null;
  }
  console.log("[MONITOR] System monitoring stopped");
}

// Note: Initialization moved to be called explicitly when needed
// to avoid global scope issues in Cloudflare Workers
// Call initSystemMonitoring() explicitly in your application startup
