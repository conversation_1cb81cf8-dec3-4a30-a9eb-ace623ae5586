import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/cloudflare";
import { and, desc, eq } from "drizzle-orm";
import { respData, respErr } from "~/lib/api/resp";
import { requireUser } from "~/lib/auth/middleware.server";
import { createDb } from "~/lib/db/db";
import { conversations, messages, type NewConversation, type NewMessage } from "~/lib/db/schema";

// Type definitions for request body
interface ConversationBody {
  action: "create" | "add-message" | "update-title" | "archive";
  conversationId?: string;
  title?: string;
  message?: {
    role: string;
    content: string;
    model?: string;
    provider?: string;
    tokenCount?: number;
    metadata?: any;
  };
  model?: string;
  provider?: string;
}

// GET: Get all conversations for the current user
export async function loader({ request, context }: LoaderFunctionArgs) {
  try {
    const user = await requireUser(request);
    const userUuid = user.uuid;

    // Check if DATABASE_URL is available
    if (!context.cloudflare?.env?.DATABASE_URL) {
      console.error("DATABASE_URL not found in environment");
      return respErr("Database configuration error");
    }

    const db = createDb({
      url: context.cloudflare.env.DATABASE_URL,
      connectionTimeoutMillis: 10000, // 10 seconds
      enableLogging: true,
    });

    const userConversations = await db
      .select({
        id: conversations.id,
        title: conversations.title,
        model: conversations.model,
        provider: conversations.provider,
        isArchived: conversations.isArchived,
        lastMessageAt: conversations.lastMessageAt,
        createdAt: conversations.createdAt,
        updatedAt: conversations.updatedAt,
      })
      .from(conversations)
      .where(and(eq(conversations.userUuid, userUuid), eq(conversations.isArchived, false)))
      .orderBy(desc(conversations.lastMessageAt), desc(conversations.createdAt));

    return respData({
      conversations: userConversations,
    });
  } catch (error) {
    console.error("Failed to fetch conversations:", error);

    // Return empty conversations instead of error to prevent UI crash
    return respData({
      conversations: [],
      error: "Failed to load conversations, please refresh the page",
    });
  }
}

// POST: Create a new conversation or add message to existing conversation
export async function action({ request, context }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return respErr("Method not allowed");
  }

  try {
    const user = await requireUser(request);
    const userUuid = user.uuid;

    // Check if DATABASE_URL is available
    if (!context.cloudflare?.env?.DATABASE_URL) {
      console.error("DATABASE_URL not found in environment");
      return respErr("Database configuration error");
    }

    const db = createDb({
      url: context.cloudflare.env.DATABASE_URL,
      connectionTimeoutMillis: 10000, // 10 seconds
      enableLogging: true,
    });
    const body = (await request.json()) as ConversationBody;
    const { action, conversationId, title, message, model, provider } = body;

    switch (action) {
      case "create": {
        // Create a new conversation
        if (!title) {
          return respErr("Title is required");
        }

        const newConversation: NewConversation = {
          userUuid,
          title,
          model: model || null,
          provider: provider || null,
        };

        const [conversation] = await db.insert(conversations).values(newConversation).returning();

        return respData({
          conversation,
        });
      }

      case "add-message": {
        // Add a message to an existing conversation
        if (!conversationId || !message || !message.role || !message.content) {
          return respErr("Conversation ID and message details are required");
        }

        // Verify the conversation belongs to the user
        const conversation = await db
          .select()
          .from(conversations)
          .where(and(eq(conversations.id, conversationId), eq(conversations.userUuid, userUuid)))
          .limit(1);

        if (conversation.length === 0) {
          return respErr("Conversation not found");
        }

        const newMessage: NewMessage = {
          conversationId,
          role: message.role,
          content: message.content,
          model: message.model || null,
          provider: message.provider || null,
          tokenCount: message.tokenCount || null,
          metadata: message.metadata || null,
        };

        const [savedMessage] = await db.insert(messages).values(newMessage).returning();

        return respData({
          message: savedMessage,
        });
      }

      case "update-title": {
        // Update conversation title
        if (!conversationId || !title) {
          return respErr("Conversation ID and title are required");
        }

        const [updatedConversation] = await db
          .update(conversations)
          .set({ title, updatedAt: new Date() })
          .where(and(eq(conversations.id, conversationId), eq(conversations.userUuid, userUuid)))
          .returning();

        if (!updatedConversation) {
          return respErr("Conversation not found");
        }

        return respData({
          conversation: updatedConversation,
        });
      }

      case "archive": {
        // Archive a conversation
        if (!conversationId) {
          return respErr("Conversation ID is required");
        }

        const [archivedConversation] = await db
          .update(conversations)
          .set({ isArchived: true, updatedAt: new Date() })
          .where(and(eq(conversations.id, conversationId), eq(conversations.userUuid, userUuid)))
          .returning();

        if (!archivedConversation) {
          return respErr("Conversation not found");
        }

        return respData({
          conversation: archivedConversation,
        });
      }

      default:
        return respErr("Invalid action");
    }
  } catch (error) {
    console.error("Conversation API error:", error);
    return respErr("Failed to process request");
  }
}
