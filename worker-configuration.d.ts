// Generated by Wrang<PERSON> by running `wrangler types`

interface Env {
  DATABASE_URL: string;
  OPENAI_API_KEY: string;
  DEEPSEEK_API_KEY: string;
  OPENROUTER_API_KEY: string;
  SILICONFLOW_API_KEY: string;
  SILICONFLOW_BASE_URL: string;
  REPLICATE_API_TOKEN: string;
  STRIPE_PUBLIC_KEY: string;
  STRIPE_SECRET_KEY: string;
  GA_TRACKING_ID: string;
  GOOGLE_CLIENT_ID: string;
  ONE_TAP_ENABLED: string;
  NODE_ENV: string;
  BYPASS_AUTH: string;
  R2_BUCKET: R2Bucket;
  AI: Ai;
}
