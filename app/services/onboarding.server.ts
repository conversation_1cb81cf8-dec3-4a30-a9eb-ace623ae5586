/**
 * User Onboarding Service
 * Handles new user welcome flow, email sending, and initial setup
 */

import { eq } from "drizzle-orm";
import type { Database } from "~/lib/db/db";
import { users } from "~/lib/db/schema";
import { emailService } from "~/lib/email/service.server";
import { welcomeTemplate } from "~/lib/email/templates/welcome";
import { CreditsTransType, increaseCredits } from "~/services/user-management.server";

export interface OnboardingData {
  user: {
    id: string;
    uuid: string;
    name: string;
    email: string;
    credits: number;
    inviteCode?: string;
  };
  isNewUser: boolean;
  inviteBonus?: boolean;
  invitedBy?: string;
}

export interface WelcomeEmailData {
  name: string;
  email: string;
  credits: number;
  inviteCode?: string;
  inviteBonus?: boolean;
  dashboardUrl: string;
  docsUrl: string;
  supportUrl: string;
}

/**
 * Send welcome email to new user
 */
export async function sendWelcomeEmail(
  emailData: WelcomeEmailData,
  env?: Record<string, string | undefined>
): Promise<{ success: boolean; error?: string }> {
  try {
    const templateVariables = {
      name: emailData.name,
      credits: emailData.credits.toString(),
      inviteCode: emailData.inviteCode || "",
      inviteBonus: emailData.inviteBonus ? "true" : "",
      dashboardUrl: emailData.dashboardUrl,
      docsUrl: emailData.docsUrl,
      supportUrl: emailData.supportUrl,
    };

    const result = await emailService.sendEmail({
      to: {
        email: emailData.email,
        name: emailData.name,
      },
      from: {
        email: env?.FROM_EMAIL || "<EMAIL>",
        name: "AI SaaS Starter",
      },
      subject: welcomeTemplate.subject(templateVariables),
      html: welcomeTemplate.html(templateVariables),
      text: welcomeTemplate.text(templateVariables),
    });

    if (result.success) {
      console.log(`Welcome email sent successfully to ${emailData.email}`);
      return { success: true };
    } else {
      console.error(`Failed to send welcome email to ${emailData.email}:`, result.error);
      return { success: false, error: result.error };
    }
  } catch (error) {
    console.error("Error sending welcome email:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * Process new user onboarding
 */
export async function processNewUserOnboarding(
  userData: {
    id: string;
    uuid: string;
    name: string;
    email: string;
    inviteCode?: string;
    invitedBy?: string;
  },
  db: Database,
  env?: Record<string, string | undefined>
): Promise<{ success: boolean; data?: OnboardingData; error?: string }> {
  try {
    const baseUrl = env?.WEB_URL || "http://localhost:3000";
    let totalCredits = 100; // Base credits for new users
    let inviteBonus = false;

    // Process invite bonus if applicable
    if (userData.invitedBy) {
      try {
        // Give bonus credits to new user
        await increaseCredits(
          {
            user_uuid: userData.uuid,
            trans_type: CreditsTransType.InviteBonus,
            credits: 50,
            description: "Invite bonus for new user registration",
          },
          db
        );

        // Give referral bonus to inviter
        await increaseCredits(
          {
            user_uuid: userData.invitedBy,
            trans_type: CreditsTransType.Referral,
            credits: 25,
            description: `Referral bonus for inviting ${userData.email}`,
          },
          db
        );

        totalCredits += 50;
        inviteBonus = true;
        console.log(
          `Invite bonus processed: ${userData.email} (+50), inviter ${userData.invitedBy} (+25)`
        );
      } catch (error) {
        console.error("Error processing invite bonus:", error);
        // Continue with onboarding even if invite bonus fails
      }
    }

    // Send welcome email
    const emailResult = await sendWelcomeEmail(
      {
        name: userData.name,
        email: userData.email,
        credits: totalCredits,
        inviteCode: userData.inviteCode,
        inviteBonus,
        dashboardUrl: `${baseUrl}/console/dashboard`,
        docsUrl: `${baseUrl}/docs`,
        supportUrl: `${baseUrl}/support`,
      },
      env
    );

    if (!emailResult.success) {
      console.warn(`Welcome email failed for ${userData.email}, but continuing onboarding`);
    }

    // Prepare onboarding data
    const onboardingData: OnboardingData = {
      user: {
        id: userData.id,
        uuid: userData.uuid,
        name: userData.name,
        email: userData.email,
        credits: totalCredits,
        inviteCode: userData.inviteCode,
      },
      isNewUser: true,
      inviteBonus,
      invitedBy: userData.invitedBy,
    };

    return {
      success: true,
      data: onboardingData,
    };
  } catch (error) {
    console.error("Error processing new user onboarding:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * Check if user should see onboarding flow
 */
export function shouldShowOnboarding(user: any, request: Request): boolean {
  // Check if this is a new user session
  const url = new URL(request.url);
  const isNewUser = url.searchParams.get("new_user") === "true";

  // Check if user has seen onboarding before (you can store this in user preferences)
  const hasSeenOnboarding = user.hasSeenOnboarding || false;

  // Show onboarding for new users who haven't seen it yet
  return isNewUser && !hasSeenOnboarding;
}

/**
 * Mark user as having completed onboarding
 */
export async function markOnboardingComplete(
  userUuid: string,
  db: Database
): Promise<{ success: boolean; error?: string }> {
  try {
    // Update user record with onboarding completion flag
    await db
      .update(users)
      .set({
        hasSeenOnboarding: true,
        onboardingCompletedAt: new Date(),
        updatedAt: new Date(),
      })
      .where(eq(users.uuid, userUuid));

    console.log(`User ${userUuid} completed onboarding`);
    return { success: true };
  } catch (error) {
    console.error("Error marking onboarding complete:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * Generate invite code for user
 */
export function generateInviteCode(): string {
  const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
  let result = "";
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * Validate invite code format
 */
export function isValidInviteCode(code: string): boolean {
  return /^[A-Z0-9]{8}$/.test(code);
}
