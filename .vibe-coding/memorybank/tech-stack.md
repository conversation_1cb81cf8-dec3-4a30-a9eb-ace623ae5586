# Gin AI 后端技术栈

## 核心技术

### 编程语言
- **Go**: 1.18+ 版本
  - 特性: 强类型、并发支持、垃圾回收、跨平台
  - 优势: 高性能、内存效率高、编译速度快、适合微服务架构

### Web 框架
- **Gin**: v1.8.x
  - 特性: 轻量级HTTP框架、中间件支持、路由分组、参数验证
  - 优势: 性能卓越、API友好、错误管理完善、大型社区支持

## 数据存储

### 关系型数据库
- **PostgreSQL**: 14.x
  - 用途: 存储用户数据、认证信息、Token使用记录
  - 特性: 强大的SQL支持、JSON数据类型、事务完整性
  - 驱动: [pgx](https://github.com/jackc/pgx) v4.x

### 对象存储
- **Cloudflare R2**
  - 用途: 存储用户上传的文件和文档
  - 特性: S3兼容API、无出口费用、全球分布
  - SDK: AWS SDK for Go v2 或官方API

### 缓存
- **Redis**: 6.2.x
  - 用途: 缓存AI请求结果、限流控制、会话存储
  - 特性: 高性能、支持多种数据结构、发布/订阅机制
  - 客户端: [go-redis](https://github.com/go-redis/redis) v8.x

## 认证与安全

### 认证
- **JWT (JSON Web Tokens)**
  - 实现库: [golang-jwt/jwt](https://github.com/golang-jwt/jwt) v4.x
  - 用途: 用户认证和授权、API访问控制
  - 特性: 无状态、可扩展、安全传输用户信息

### 密码加密
- **bcrypt**
  - 实现库: [golang.org/x/crypto/bcrypt](https://pkg.go.dev/golang.org/x/crypto/bcrypt)
  - 用途: 安全存储用户密码
  - 特性: 自适应哈希函数、抗暴力破解

### API安全
- **CORS中间件**: Gin官方支持
- **速率限制**: [ulule/limiter](https://github.com/ulule/limiter)
- **安全标头**: [secure](https://github.com/unrolled/secure)
- **HTTPS/TLS**: 通过反向代理或直接在应用中实现

## AI集成

### AI服务
- **OpenAI API**
  - 客户端库: 官方Go SDK或自定义HTTP客户端
  - 功能: 模型推理、文本生成、嵌入向量
  - 模型: GPT-4、GPT-3.5-Turbo、Embeddings等

### 适配器
- **自定义适配器接口**
  - 目的: 支持多种AI服务提供商
  - 实现: Factory模式、策略模式
  - 支持: OpenAI、Azure OpenAI、本地模型等

## 操作与监控

### 日志
- **zap**: [go.uber.org/zap](https://pkg.go.dev/go.uber.org/zap)
  - 特性: 结构化日志、高性能、多级别日志
  - 配置: 开发和生产环境配置、JSON格式输出

### 监控
- **Prometheus**: 收集和暴露指标
  - 客户端: [prometheus/client_golang](https://github.com/prometheus/client_golang)
  - 指标: 请求计数、延迟、错误率、Token使用量

### 追踪
- **OpenTelemetry**
  - 实现: [go.opentelemetry.io](https://pkg.go.dev/go.opentelemetry.io)
  - 特性: 分布式追踪、服务监控、性能分析

## 部署与运维

### 容器化
- **Docker**
  - 镜像构建: 多阶段构建、Alpine基础镜像
  - 配置: 环境变量、配置文件挂载、健康检查

### 容器编排
- **Docker Compose** (开发环境)
  - 服务: API、数据库、Redis、反向代理
  - 配置: 网络隔离、数据持久化、环境变量

### CI/CD
- **GitHub Actions**
  - 流程: 单元测试、集成测试、构建、部署
  - 阶段: 开发、测试、生产环境

## 开发工具

### 依赖管理
- **Go Modules**
  - 特性: 版本控制、依赖解析、可重现构建

### 代码质量
- **golangci-lint**: 代码静态分析
- **go test**: 单元测试和集成测试
- **gomock**: 单元测试模拟
- **testcontainers-go**: 集成测试容器化依赖

### API文档
- **Swagger/OpenAPI**
  - 实现: [swaggo/swag](https://github.com/swaggo/swag)
  - 集成: [swaggo/gin-swagger](https://github.com/swaggo/gin-swagger)
  - 功能: API文档自动生成、交互式测试

## 项目布局
采用标准Go项目布局:
```
gin-ai-backend/
├── cmd/
├── internal/
├── pkg/
├── api/
├── docs/
├── config/
└── scripts/
```

## 第三方服务集成

### 存储服务
- **Cloudflare R2**: 文件存储
- **Cloudflare Workers**: 边缘计算和CDN

### 监控与告警
- **Grafana Cloud**: 仪表盘和可视化
- **PagerDuty/Opsgenie**: 告警通知

### 其他
- **SendGrid/Mailgun**: 邮件发送
- **Stripe**: 支付集成 (如果需要)
- **Sentry**: 错误追踪和报告