// Main schema file - now uses modular schema structure
// This file has been refactored into domain-specific modules for better maintainability

// Re-export everything from the modular schemas
export * from "./schemas";

// Keep this file for backward compatibility
// The actual schema definitions are now in ./schemas/ directory:
//
// - ./schemas/shared.ts      - Shared enums and types
// - ./schemas/users.ts       - User, auth, roles, sessions
// - ./schemas/billing.ts     - Payments, subscriptions, orders
// - ./schemas/chat.ts        - Conversations, messages, AI content
// - ./schemas/content.ts     - Posts, feedback, content management
// - ./schemas/monitoring.ts  - API usage, analytics, rate limiting
//
// For new development, prefer importing from specific schema files:
// import { users, sessions } from "~/lib/db/schemas/users"
// import { orders, subscriptions } from "~/lib/db/schemas/billing"
//
// This file maintains backward compatibility for existing imports:
// import { users, orders, conversations } from "~/lib/db/schema"
