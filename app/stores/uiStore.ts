import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
import type { Notification, UIState } from "./types";

const initialState = {
  sidebarOpen: false,
  notifications: [],
};

// Simplified store without notifications to avoid global scope issues
export const useUIStore = create<UIState>()(
  devtools(
    (set) => ({
      ...initialState,

      toggleSidebar: () => {
        set((state) => ({ sidebarOpen: !state.sidebarOpen }), false, "toggleSidebar");
      },

      setSidebarOpen: (open) => {
        set({ sidebarOpen: open }, false, "setSidebarOpen");
      },

      // Disabled notification functions to avoid global scope issues
      addNotification: () => {
        // Notifications disabled for Cloudflare Workers compatibility
        console.log("Notifications temporarily disabled");
      },

      removeNotification: () => {
        // Notifications disabled for Cloudflare Workers compatibility
      },

      clearNotifications: () => {
        // Notifications disabled for Cloudflare Workers compatibility
      },

      reset: () => {
        set(initialState, false, "reset");
      },
    }),
    {
      name: "ui-store",
    }
  )
);

// Selector functions
export const selectSidebarOpen = (state: UIState) => state.sidebarOpen;
export const selectNotifications = (state: UIState) => state.notifications;

// Convenience hooks
export const useSidebarOpen = () => useUIStore(selectSidebarOpen);
export const useNotifications = () => useUIStore(selectNotifications);

// Actions hooks
export const useUIActions = () => {
  const toggleSidebar = useUIStore((state) => state.toggleSidebar);
  const setSidebarOpen = useUIStore((state) => state.setSidebarOpen);
  const addNotification = useUIStore((state) => state.addNotification);
  const removeNotification = useUIStore((state) => state.removeNotification);
  const clearNotifications = useUIStore((state) => state.clearNotifications);
  const reset = useUIStore((state) => state.reset);

  return {
    toggleSidebar,
    setSidebarOpen,
    addNotification,
    removeNotification,
    clearNotifications,
    reset,
  };
};
