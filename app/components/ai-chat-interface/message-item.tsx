import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, User } from "lucide-react";
import { memo, useState } from "react";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { cn } from "~/lib/utils/utils";
import type { Message } from "./types";

interface MessageItemProps {
  message: Message;
  isLast?: boolean;
}

export const MessageItem = memo(function MessageItem({ message, isLast }: MessageItemProps) {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(message.content);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error("Failed to copy message:", error);
    }
  };

  const formatTimestamp = (timestamp: string) => {
    try {
      return new Date(timestamp).toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      });
    } catch {
      return "";
    }
  };

  return (
    <div
      className={cn(
        "group flex gap-4 p-4 hover:bg-muted/30 transition-colors",
        message.role === "assistant" && "bg-muted/10",
        isLast && "mb-20"
      )}
      role="article"
      aria-label={`${message.role === "user" ? "User" : "Assistant"} message`}
    >
      {/* Avatar */}
      <div className="flex-shrink-0">
        <div
          className={cn(
            "w-8 h-8 rounded-lg flex items-center justify-center",
            message.role === "user"
              ? "bg-primary text-primary-foreground"
              : "bg-muted border border-border"
          )}
        >
          {message.role === "user" ? (
            <User className="h-4 w-4" />
          ) : (
            <Sparkles className="h-4 w-4" />
          )}
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 min-w-0 space-y-2">
        {/* Message Header */}
        <div className="flex items-center gap-2 text-sm">
          <span className="font-medium text-foreground">
            {message.role === "user" ? "You" : "Assistant"}
          </span>
          {message.provider && (
            <Badge variant="outline" className="text-xs px-2 py-0.5">
              {message.provider}
            </Badge>
          )}
          {message.model && (
            <span className="text-xs text-muted-foreground font-mono">{message.model}</span>
          )}
          <span className="text-xs text-muted-foreground ml-auto">
            {formatTimestamp(message.createdAt)}
          </span>
        </div>

        {/* Message Content */}
        <div className="prose prose-sm max-w-none dark:prose-invert">
          {message.imageUrl ? (
            <div className="space-y-3">
              <img
                src={message.imageUrl}
                alt="Generated content"
                className="max-w-md rounded-lg border border-border"
                loading="lazy"
              />
              {message.content && (
                <p className="text-sm text-muted-foreground">{message.content}</p>
              )}
            </div>
          ) : (
            <div className="whitespace-pre-wrap break-words text-sm leading-6">
              {message.content}
            </div>
          )}
        </div>

        {/* Message Actions */}
        <div className="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleCopy}
            className="h-7 px-2 text-xs"
            aria-label="Copy message content"
          >
            <Copy className="h-3 w-3 mr-1" />
            {copied ? "Copied!" : "Copy"}
          </Button>
          {message.tokenCount && (
            <span className="text-xs text-muted-foreground">{message.tokenCount} tokens</span>
          )}
        </div>
      </div>
    </div>
  );
});

export default MessageItem;
