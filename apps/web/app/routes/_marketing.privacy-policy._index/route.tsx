import { MetaFunction, useLoaderData } from '@remix-run/react';
import type { LoaderFunctionArgs } from '@remix-run/server-runtime';
import { createCmsClient } from '@kit/cms';
import { useTranslation } from 'react-i18next';
import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { LegalContentRenderer } from '~/routes/_marketing/_components/legal-content-renderer';
import { SitePageHeader } from '~/routes/_marketing/_components/site-page-header';

export const meta: MetaFunction<typeof loader> = ({ data }) => {
  return [
    {
      title: data?.title,
    },
  ];
};

export const loader = async function ({ request }: LoaderFunctionArgs) {
  const { t, resolvedLanguage } = await createI18nServerInstance(request);
  const cms = await createCmsClient();
  
  // Get current language from i18n
  const locale = resolvedLanguage || 'en';
  
  // First try to get language-specific content
  let contentItem = await cms.getContentItemBySlug({
    slug: `privacy-policy-${locale}`,
    collection: 'legal'
  });
  
  // If language-specific content not found, fall back to default
  if (!contentItem) {
    contentItem = await cms.getContentItemBySlug({
      slug: 'privacy-policy',
      collection: 'legal'
    });
  }
  
  return {
    title: contentItem?.title || t('marketing:privacyPolicy'),
    subtitle: t('marketing:privacyPolicyDescription'),
    content: contentItem?.content as string | undefined
  };
};

export default function PrivacyPolicyPage() {
  const data = useLoaderData<typeof loader>();
  const { t } = useTranslation('marketing');

  return (
    <div>
      <SitePageHeader title={data.title} subtitle={data.subtitle} />

      <div className={'container mx-auto py-8'}>
        {data.content ? (
          <LegalContentRenderer content={data.content} />
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-600 mb-2">{t('contentNotAvailable')}</p>
            <p className="text-sm text-gray-500">{t('checkBackLater')}</p>
          </div>
        )}
      </div>
    </div>
  );
}
