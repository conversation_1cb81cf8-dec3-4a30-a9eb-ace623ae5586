import { Link } from '@remix-run/react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Head<PERSON>, Bar<PERSON>hart2, <PERSON>, <PERSON> } from 'lucide-react';
import type React from "react";

import { PricingTable } from '@kit/billing-gateway/marketing';
import {
  C<PERSON>Button,
  <PERSON>,
  <PERSON>ll,
  SecondaryHero,
} from '@kit/ui/marketing';
import { Trans } from '@kit/ui/trans';

import billingConfig from '~/config/billing.config';
import pathsConfig from '~/config/paths.config';

// Create a simplified version of GradientSecondaryText
const GradientSecondaryText = ({ children }: { children: React.ReactNode }) => (
  <span className="bg-gradient-to-r from-indigo-600 to-blue-600 dark:from-indigo-400 dark:to-blue-400 bg-clip-text text-transparent">
    {children}
  </span>
)

export default function Index() {
  return (
    <div className={'mt-4 flex flex-col space-y-24 py-14'}>
      <Hero
        pill={
          <Pill label={'NEW EXPERIENCE'}>
            <span>Revolutionary app generating personalized sleep audio based on your physiological state</span>
          </Pill>
        }
        title={
          <>
            <span>Your Personalized Sleep</span>
            <span>Audio Experience Journey</span>
          </>
        }
        subtitle={
          <span>
            ZenKira utilizes advanced AI and real-time biofeedback technology to create unique natural 
            audio experiences tailored to your current state and sleep needs, helping you achieve deeper relaxation and better sleep quality.
          </span>
        }
        cta={<MainCallToActionButton />}
        image={
          <div
            className={
              'dark:border-primary/10 rounded-2xl border border-gray-200 bg-gray-50 dark:bg-gray-800 flex items-center justify-center p-8 h-[400px] overflow-hidden'
            }
          >
            <img 
              src="/images/landingpage/hero-peaceful-sleep.webp" 
              alt="ZenKira app interface showing the main dashboard with sleep audio selection, real-time heart rate monitoring, and personalized recommendations"
              className="w-full h-full object-contain"
            />
          </div>
        }
      />

      <FeatureSection />

      <div className="container mx-auto bg-gradient-to-r from-indigo-50 to-blue-50 dark:from-indigo-950/30 dark:to-blue-950/30 rounded-2xl p-8">
        <div className="flex flex-col space-y-8 md:flex-row md:space-y-0 md:space-x-8 items-center">
          <div className="md:w-1/2">
            <h3 className="text-2xl font-bold mb-4">Why Choose ZenKira?</h3>
            <ul className="space-y-4">
              <li className="flex items-start">
                <div className="bg-indigo-100 dark:bg-indigo-900/50 p-2 rounded-full mr-3 mt-1">
                  <Moon className="h-4 w-4 text-indigo-600 dark:text-indigo-300" />
                </div>
                <div>
                  <h4 className="font-medium">Scientifically Validated Sleep Improvement</h4>
                  <p className="text-gray-600 dark:text-gray-300">Clinically proven to reduce average time to fall asleep by 43% and improve sleep quality by 35%</p>
                </div>
              </li>
              <li className="flex items-start">
                <div className="bg-indigo-100 dark:bg-indigo-900/50 p-2 rounded-full mr-3 mt-1">
                  <Headphones className="h-4 w-4 text-indigo-600 dark:text-indigo-300" />
                </div>
                <div>
                  <h4 className="font-medium">Rich Natural Audio Library</h4>
                  <p className="text-gray-600 dark:text-gray-300">Over 200 high-quality natural sound effects, from rain sounds to ocean waves, from forest ambience to meditation music</p>
                </div>
              </li>
              <li className="flex items-start">
                <div className="bg-indigo-100 dark:bg-indigo-900/50 p-2 rounded-full mr-3 mt-1">
                  <Heart className="h-4 w-4 text-indigo-600 dark:text-indigo-300" />
                </div>
                <div>
                  <h4 className="font-medium">Smart Device Compatibility</h4>
                  <p className="text-gray-600 dark:text-gray-300">Seamlessly connects with mainstream smartwatches and health monitoring devices to obtain real-time biometric data</p>
                </div>
              </li>
            </ul>
          </div>
          <div className="md:w-1/2 bg-white dark:bg-gray-800 rounded-xl p-6 shadow-md">
            <div className="text-center mb-4">
              <span className="inline-block bg-indigo-100 dark:bg-indigo-900/50 text-indigo-800 dark:text-indigo-200 px-3 py-1 rounded-full text-sm font-medium">User Feedback</span>
            </div>
            <blockquote className="italic text-gray-700 dark:text-gray-300 mb-4">
              "After using ZenKira for three weeks, my sleep quality has significantly improved. The app automatically adjusts the audio based on my heart rate, making me feel very relaxed. Now I can fall asleep peacefully every night."
            </blockquote>
            <div className="flex items-center">
              <div className="h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-700 mr-3"></div>
              <div>
                <p className="font-medium">Sarah L.</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">ZenKira user for 3 months</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className={'container mx-auto'}>
        <div
          className={
            'flex flex-col items-center justify-center space-y-16 py-16'
          }
        >
          <SecondaryHero
            pill={<Pill>Start for free. No credit card required.</Pill>}
            heading="Fair pricing for all types of users"
            subheading="Begin with our free plan and upgrade to premium services whenever you're ready."
          />

          <div className={'w-full'}>
            <PricingTable
              config={billingConfig}
              paths={{
                signUp: pathsConfig.auth.signUp,
                return: pathsConfig.app.home,
              }}
            />
          </div>
        </div>
      </div>

      <div className="container mx-auto bg-gray-50 dark:bg-gray-900/50 rounded-2xl p-8">
        <div className="text-center mb-8">
          <h3 className="text-2xl font-bold mb-2">Frequently Asked Questions</h3>
          <p className="text-gray-600 dark:text-gray-300">Common questions about ZenKira</p>
        </div>
        <div className="grid md:grid-cols-2 gap-6">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm">
            <h4 className="font-medium mb-2">How does ZenKira connect with my smartwatch?</h4>
            <p className="text-gray-600 dark:text-gray-300">ZenKira supports Bluetooth connection with mainstream smartwatches like Apple Watch, Fitbit, and Garmin. Simply follow the connection wizard in the app for easy setup.</p>
          </div>
          <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm">
            <h4 className="font-medium mb-2">Can I customize the audio content?</h4>
            <p className="text-gray-600 dark:text-gray-300">Absolutely. In addition to AI-generated audio, you can adjust audio types, volume, and mixing effects to create sleep audio that perfectly matches your personal preferences.</p>
          </div>
          <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm">
            <h4 className="font-medium mb-2">Does ZenKira collect my health data?</h4>
            <p className="text-gray-600 dark:text-gray-300">ZenKira collects your heart rate and sleep data to provide personalized services, but we strictly adhere to privacy protection regulations. All data is encrypted and never shared with third parties.</p>
          </div>
          <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm">
            <h4 className="font-medium mb-2">What's the difference between free and paid versions?</h4>
            <p className="text-gray-600 dark:text-gray-300">The free version provides basic sleep audio and simple sleep analysis, while the paid version offers more advanced features such as real-time biofeedback adjustment, detailed sleep reports, and a richer audio library.</p>
          </div>
        </div>
      </div>
    </div>
  );
}

function FeatureSection() {
  return (
    <div className={"container mx-auto"}>
      <div className={"flex flex-col space-y-16 xl:space-y-32 2xl:space-y-36"}>
        <div className="text-center mb-16">
          <div className="inline-flex items-center justify-center gap-2 bg-indigo-50 dark:bg-indigo-950/30 px-4 py-2 rounded-full mb-4">
            <Moon className="h-4 w-4 text-indigo-600 dark:text-indigo-400" />
            <span className="text-sm font-medium text-indigo-600 dark:text-indigo-400">Complete Sleep Solution</span>
          </div>

          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            <span className="font-semibold dark:text-white">Next-generation intelligent sleep aid application</span>
          </h2>

          <p className="max-w-2xl mx-auto text-gray-600 dark:text-gray-300">
            <GradientSecondaryText>
              Combining artificial intelligence with real-time biofeedback technology to bring you an unprecedented
              personalized sleep experience.
            </GradientSecondaryText>
          </p>
        </div>

        {/* Feature Cards - Using a cleaner grid layout */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 lg:gap-12">
          {/* Feature 1: Personalized Audio Experience */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden transition-all hover:shadow-2xl hover:-translate-y-1">
            <div className="aspect-video w-full relative">
              <img 
                src="/images/landingpage/feature-audio-interface.webp" 
                alt="Modern audio interface with various natural sound options" 
                className="w-full h-full object-cover"
              />
            </div>
            <div className="p-6">
              <div className="flex items-center gap-2 mb-3">
                <div className="bg-indigo-100 dark:bg-indigo-900/50 p-2 rounded-full">
                  <Headphones className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
                </div>
                <h3 className="text-xl font-bold">Personalized Audio Experience</h3>
              </div>
              <p className="text-gray-600 dark:text-gray-300">
                ZenKira dynamically generates customized natural audio content based on your real-time heart rate and
                other physiological data, including rain sounds, ocean waves, forest ambience, and meditation music to
                help you fall asleep faster.
              </p>
            </div>
          </div>

          {/* Feature 2: Real-time Biofeedback Technology */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden transition-all hover:shadow-2xl hover:-translate-y-1">
            <div className="aspect-video w-full relative">
              <img 
                src="/images/landingpage/feature-heart-rate-monitor.webp" 
                alt="Smartwatch connected to a phone app displaying heart rate monitoring interface" 
                className="w-full h-full object-cover"
              />
            </div>
            <div className="p-6">
              <div className="flex items-center gap-2 mb-3">
                <div className="bg-red-100 dark:bg-red-900/50 p-2 rounded-full">
                  <Heart className="h-5 w-5 text-red-600 dark:text-red-400" />
                </div>
                <h3 className="text-xl font-bold">Real-time Biofeedback Technology</h3>
              </div>
              <p className="text-gray-600 dark:text-gray-300">
                Monitors heart rate through smartwatches or other wearable devices to assess sleep state and
                continuously optimizes audio effects for your best sleep experience. The system adjusts audio content in
                real-time based on your heart rate changes.
              </p>
            </div>
          </div>

          {/* Feature 3: AI-Driven Technology */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden transition-all hover:shadow-2xl hover:-translate-y-1">
            <div className="aspect-video w-full relative">
              <img 
                src="/images/landingpage/feature-ai-analysis.webp" 
                alt="AI analysis of sleep patterns visualization" 
                className="w-full h-full object-cover"
              />
            </div>
            <div className="p-6">
              <div className="flex items-center gap-2 mb-3">
                <div className="bg-purple-100 dark:bg-purple-900/50 p-2 rounded-full">
                  <Brain className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                </div>
                <h3 className="text-xl font-bold">AI-Driven Technology</h3>
              </div>
              <p className="text-gray-600 dark:text-gray-300">
                Leverages advanced artificial intelligence algorithms to analyze your sleep patterns and preferences. As
                you use the system more, it learns more about your needs and provides increasingly accurate personalized
                services and recommendations.
              </p>
            </div>
          </div>

          {/* Feature 4: Sleep Quality Analysis */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden transition-all hover:shadow-2xl hover:-translate-y-1">
            <div className="aspect-video w-full relative">
              <img 
                src="/images/landingpage/feature-sleep-analysis.webp" 
                alt="Detailed sleep analysis report interface" 
                className="w-full h-full object-cover"
              />
            </div>
            <div className="p-6">
              <div className="flex items-center gap-2 mb-3">
                <div className="bg-blue-100 dark:bg-blue-900/50 p-2 rounded-full">
                  <BarChart2 className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                </div>
                <h3 className="text-xl font-bold">Sleep Quality Analysis</h3>
              </div>
              <p className="text-gray-600 dark:text-gray-300">
                Provides comprehensive sleep analysis and improvement suggestions to help you understand your sleep
                patterns and continuously improve. View detailed sleep reports every morning, including deep sleep
                duration, sleep efficiency, and improvement recommendations.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

function MainCallToActionButton() {
  return (
    <div className={'flex space-x-4'}>
      <CtaButton>
        <Link to={'/auth/sign-up'}>
          <span className={'flex items-center space-x-0.5'}>
            <span>
              <Trans i18nKey={'common:getStarted'} />
            </span>

            <ArrowRightIcon
              className={
                'animate-in fade-in slide-in-from-left-8 h-4' +
                ' zoom-in fill-mode-both delay-1000 duration-1000'
              }
            />
          </span>
        </Link>
      </CtaButton>

      <CtaButton variant={'link'}>
        <Link to={'/contact'}>
          <Trans i18nKey={'common:contactUs'} />
        </Link>
      </CtaButton>
    </div>
  );
}
