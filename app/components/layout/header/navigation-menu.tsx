import { Link } from "@remix-run/react";
import { buttonVariants } from "~/components/ui/button";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from "~/components/ui/navigation-menu";
import { cn } from "~/lib/utils/utils";
import type { NavItem } from "./types";

interface NavigationMenuComponentProps {
  items: NavItem[];
  className?: string;
  onItemClick?: () => void;
}

export function NavigationMenuComponent({
  items,
  className,
  onItemClick,
}: NavigationMenuComponentProps) {
  return (
    <NavigationMenu className={cn("mx-6", className)} aria-label="Primary navigation">
      <NavigationMenuList>
        {items.map((item, i) => {
          if (item.children && item.children.length > 0) {
            return (
              <NavigationMenuItem key={`${item.title}-${i}`}>
                <NavigationMenuTrigger className="text-foreground font-medium">
                  {item.icon && <span className="mr-2">{item.icon}</span>}
                  {item.title}
                </NavigationMenuTrigger>
                <NavigationMenuContent>
                  <ul className="w-80 p-3">
                    {item.children.map((child, ii) => (
                      <li key={`${child.title}-${ii}`}>
                        <NavigationMenuLink asChild>
                          <Link
                            to={child.url}
                            target={child.target}
                            onClick={onItemClick}
                            className="flex select-none gap-4 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                          >
                            {child.icon && <span className="mt-1 shrink-0">{child.icon}</span>}
                            <div>
                              <div className="text-sm font-semibold">{child.title}</div>
                              {child.description && (
                                <p className="text-sm leading-snug text-muted-foreground">
                                  {child.description}
                                </p>
                              )}
                            </div>
                          </Link>
                        </NavigationMenuLink>
                      </li>
                    ))}
                  </ul>
                </NavigationMenuContent>
              </NavigationMenuItem>
            );
          }

          return (
            <NavigationMenuItem key={`${item.title}-${i}`}>
              <NavigationMenuLink asChild>
                <Link
                  to={item.url}
                  target={item.target}
                  onClick={onItemClick}
                  className={cn(
                    "text-foreground font-medium flex items-center",
                    navigationMenuTriggerStyle(),
                    buttonVariants({ variant: "ghost" })
                  )}
                >
                  {item.icon && <span className="mr-2">{item.icon}</span>}
                  {item.title}
                </Link>
              </NavigationMenuLink>
            </NavigationMenuItem>
          );
        })}
      </NavigationMenuList>
    </NavigationMenu>
  );
}
