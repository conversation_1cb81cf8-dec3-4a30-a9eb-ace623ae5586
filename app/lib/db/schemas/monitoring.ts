// Monitoring, analytics, and usage tracking schemas
import { relations } from "drizzle-orm";
import {
  decimal,
  index,
  integer,
  jsonb,
  pgTable,
  uuid as pgUuid,
  serial,
  text,
  timestamp,
  varchar,
} from "drizzle-orm/pg-core";
import { users } from "./users";

// API usage tracking table
export const apiUsage = pgTable(
  "api_usage",
  {
    id: serial("id").primaryKey(),
    userId: pgUuid("user_id").references(() => users.id, { onDelete: "set null" }),
    apiKeyId: pgUuid("api_key_id"), // Reference to API key if used
    endpoint: varchar("endpoint", { length: 255 }).notNull(),
    method: varchar("method", { length: 10 }).notNull(),
    statusCode: integer("status_code").notNull(),
    responseTime: integer("response_time"), // In milliseconds
    requestSize: integer("request_size"), // In bytes
    responseSize: integer("response_size"), // In bytes
    userAgent: text("user_agent"),
    ipAddress: varchar("ip_address", { length: 45 }),
    referer: text("referer"),

    // AI-specific fields
    model: varchar("model", { length: 100 }),
    provider: varchar("provider", { length: 50 }),
    tokenCount: integer("token_count"),
    cost: decimal("cost", { precision: 10, scale: 6 }), // Cost in credits or currency

    // Request metadata
    metadata: jsonb("metadata"), // Additional request data
    createdAt: timestamp("created_at").defaultNow().notNull(),
  },
  (table) => ({
    userIdx: index("api_usage_user_idx").on(table.userId),
    apiKeyIdx: index("api_usage_api_key_idx").on(table.apiKeyId),
    endpointIdx: index("api_usage_endpoint_idx").on(table.endpoint),
    statusIdx: index("api_usage_status_idx").on(table.statusCode),
    createdIdx: index("api_usage_created_idx").on(table.createdAt),
    modelIdx: index("api_usage_model_idx").on(table.model),
    providerIdx: index("api_usage_provider_idx").on(table.provider),
    // Composite index for common queries
    userEndpointIdx: index("api_usage_user_endpoint_idx").on(table.userId, table.endpoint),
    userCreatedIdx: index("api_usage_user_created_idx").on(table.userId, table.createdAt),
  })
);

// Usage statistics aggregation table
export const usageStats = pgTable(
  "usage_stats",
  {
    id: serial("id").primaryKey(),
    userId: pgUuid("user_id").references(() => users.id, { onDelete: "cascade" }),
    date: varchar("date", { length: 10 }).notNull(), // YYYY-MM-DD format
    period: varchar("period", { length: 10 }).notNull(), // "daily", "weekly", "monthly"

    // Request statistics
    totalRequests: integer("total_requests").notNull().default(0),
    successfulRequests: integer("successful_requests").notNull().default(0),
    errorRequests: integer("error_requests").notNull().default(0),
    avgResponseTime: integer("avg_response_time"), // In milliseconds

    // AI usage statistics
    totalTokens: integer("total_tokens").notNull().default(0),
    totalCost: decimal("total_cost", { precision: 10, scale: 6 }).notNull().default("0.000000"),
    uniqueModels: integer("unique_models").notNull().default(0),
    uniqueProviders: integer("unique_providers").notNull().default(0),

    // Data transfer statistics
    totalRequestSize: integer("total_request_size").notNull().default(0), // In bytes
    totalResponseSize: integer("total_response_size").notNull().default(0), // In bytes

    // Metadata
    metadata: jsonb("metadata"), // Additional aggregated data
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at")
      .defaultNow()
      .notNull()
      .$onUpdate(() => new Date()),
  },
  (table) => ({
    userIdx: index("usage_stats_user_idx").on(table.userId),
    dateIdx: index("usage_stats_date_idx").on(table.date),
    periodIdx: index("usage_stats_period_idx").on(table.period),
    userDateIdx: index("usage_stats_user_date_idx").on(table.userId, table.date),
    userPeriodIdx: index("usage_stats_user_period_idx").on(table.userId, table.period),
  })
);

// Rate limiting table
export const rateLimits = pgTable(
  "rate_limits",
  {
    id: serial("id").primaryKey(),
    identifier: varchar("identifier", { length: 255 }).notNull(), // IP, user ID, or API key
    identifierType: varchar("identifier_type", { length: 50 }).notNull(), // "ip", "user", "api_key"
    endpoint: varchar("endpoint", { length: 255 }).notNull(),
    windowStart: timestamp("window_start").notNull(),
    windowEnd: timestamp("window_end").notNull(),
    requestCount: integer("request_count").notNull().default(0),
    limitCount: integer("limit_count").notNull(),
    resetAt: timestamp("reset_at").notNull(),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at")
      .defaultNow()
      .notNull()
      .$onUpdate(() => new Date()),
  },
  (table) => ({
    identifierIdx: index("rate_limits_identifier_idx").on(table.identifier),
    endpointIdx: index("rate_limits_endpoint_idx").on(table.endpoint),
    resetIdx: index("rate_limits_reset_idx").on(table.resetAt),
    windowIdx: index("rate_limits_window_idx").on(table.windowStart, table.windowEnd),
    // Composite index for rate limit checks
    identifierEndpointIdx: index("rate_limits_identifier_endpoint_idx").on(
      table.identifier,
      table.endpoint
    ),
  })
);

// System metrics table (for overall system monitoring)
export const systemMetrics = pgTable(
  "system_metrics",
  {
    id: serial("id").primaryKey(),
    timestamp: timestamp("timestamp").defaultNow().notNull(),
    metricName: varchar("metric_name", { length: 100 }).notNull(),
    metricValue: decimal("metric_value", { precision: 15, scale: 6 }).notNull(),
    unit: varchar("unit", { length: 20 }), // "ms", "bytes", "count", "percentage"
    tags: jsonb("tags"), // Additional metric tags
    metadata: jsonb("metadata"), // Additional metric data
  },
  (table) => ({
    timestampIdx: index("system_metrics_timestamp_idx").on(table.timestamp),
    nameIdx: index("system_metrics_name_idx").on(table.metricName),
    nameTimestampIdx: index("system_metrics_name_timestamp_idx").on(
      table.metricName,
      table.timestamp
    ),
  })
);

// Relations
export const apiUsageRelations = relations(apiUsage, ({ one }) => ({
  user: one(users, {
    fields: [apiUsage.userId],
    references: [users.id],
  }),
}));

export const usageStatsRelations = relations(usageStats, ({ one }) => ({
  user: one(users, {
    fields: [usageStats.userId],
    references: [users.id],
  }),
}));
