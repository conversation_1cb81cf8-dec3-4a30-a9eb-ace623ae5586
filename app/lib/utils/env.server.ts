/**
 * Environment utilities for Cloudflare Workers compatibility
 * Handles environment variables in both development (with process.env) and production (with Cloudflare env)
 */

export interface EnvironmentContext {
  [key: string]: string | undefined;
}

/**
 * Get environment variable with fallback support
 * Works in both Cloudflare Workers and Node.js environments
 */
export function getEnvVar(
  key: string,
  env?: EnvironmentContext,
  defaultValue?: string
): string | undefined {
  // First try the provided env context (Cloudflare Workers)
  if (env && env[key] !== undefined) {
    return env[key];
  }

  // Fallback to process.env (development with Wrangler)
  if (typeof process !== "undefined" && process.env && process.env[key] !== undefined) {
    return process.env[key];
  }

  // Return default value if provided
  return defaultValue;
}

/**
 * Get required environment variable
 * Throws an error if the variable is not found
 */
export function getRequiredEnvVar(
  key: string,
  env?: EnvironmentContext,
  errorMessage?: string
): string {
  const value = getEnvVar(key, env);

  if (!value) {
    throw new Error(
      errorMessage ||
        `Required environment variable ${key} is not set. ` +
          "Please set it in your .dev.vars file for development or as a secret in your Cloudflare Worker."
    );
  }

  return value;
}

/**
 * Check if we're in development mode
 */
export function isDevelopment(env?: EnvironmentContext): boolean {
  const nodeEnv = getEnvVar("NODE_ENV", env);
  return nodeEnv === "development";
}

/**
 * Check if we're in production mode
 */
export function isProduction(env?: EnvironmentContext): boolean {
  const nodeEnv = getEnvVar("NODE_ENV", env);
  return nodeEnv === "production" || !nodeEnv; // Default to production if not set
}

/**
 * Check if we're in test mode
 */
export function isTest(env?: EnvironmentContext): boolean {
  const nodeEnv = getEnvVar("NODE_ENV", env);
  return nodeEnv === "test";
}

/**
 * Get all environment variables as an object
 * Useful for debugging or passing to other functions
 */
export function getAllEnvVars(env?: EnvironmentContext): Record<string, string | undefined> {
  const result: Record<string, string | undefined> = {};

  // Add variables from provided env context
  if (env) {
    Object.assign(result, env);
  }

  // Add variables from process.env (if available)
  if (typeof process !== "undefined" && process.env) {
    Object.assign(result, process.env);
  }

  return result;
}

/**
 * Validate required environment variables
 * Returns an array of missing variables
 */
export function validateRequiredEnvVars(
  requiredVars: string[],
  env?: EnvironmentContext
): string[] {
  const missing: string[] = [];

  for (const varName of requiredVars) {
    if (!getEnvVar(varName, env)) {
      missing.push(varName);
    }
  }

  return missing;
}

/**
 * Create environment configuration object
 * Useful for passing environment-specific settings to services
 */
export function createEnvConfig(env?: EnvironmentContext) {
  return {
    isDevelopment: isDevelopment(env),
    isProduction: isProduction(env),
    isTest: isTest(env),
    nodeEnv: getEnvVar("NODE_ENV", env, "production"),

    // Database
    databaseUrl: getEnvVar("DATABASE_URL", env),

    // Email
    resendApiKey: getEnvVar("RESEND_API_KEY", env),

    // AI Services
    openaiApiKey: getEnvVar("OPENAI_API_KEY", env),
    deepseekApiKey: getEnvVar("DEEPSEEK_API_KEY", env),
    openrouterApiKey: getEnvVar("OPENROUTER_API_KEY", env),
    siliconflowApiKey: getEnvVar("SILICONFLOW_API_KEY", env),
    replicateApiToken: getEnvVar("REPLICATE_API_TOKEN", env),

    // Auth
    stackProjectId: getEnvVar("VITE_STACK_PROJECT_ID", env),
    stackSecretKey: getEnvVar("STACK_SECRET_SERVER_KEY", env),
    neonAuthEnabled: getEnvVar("NEON_AUTH_ENABLED", env) === "true",

    // Storage
    r2BucketName: getEnvVar("R2_BUCKET_NAME", env),
  };
}

export type EnvConfig = ReturnType<typeof createEnvConfig>;
