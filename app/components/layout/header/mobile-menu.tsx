import { Link } from "@remix-run/react";
import { Menu, Search } from "lucide-react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "~/components/ui/accordion";
import { Button } from "~/components/ui/button";
import { She<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, She<PERSON><PERSON>rigger } from "~/components/ui/sheet";
import { SearchBar } from "./search-bar";
import type { ActionButton, BrandProps, NavItem, SearchProps, UserAccountProps } from "./types";
import { UserAccountMenu } from "./user-account-menu";

interface MobileMenuProps {
  brand: BrandProps;
  navigation: NavItem[];
  buttons: ActionButton[];
  search?: SearchProps;
  userAccount?: UserAccountProps;
  onClose?: () => void;
}

export function MobileMenu({
  brand,
  navigation,
  buttons,
  search,
  userAccount,
  onClose,
}: MobileMenuProps) {
  return (
    <div className="flex lg:hidden items-center justify-between w-full">
      {/* Mobile Brand */}
      <Link to={brand.url} className="flex items-center gap-2 group">
        {brand.logo ? (
          <img
            src={brand.logo}
            alt={brand.title}
            className="h-8 w-8 transition-transform group-hover:scale-105"
          />
        ) : (
          <div className="h-8 w-8 bg-primary rounded-lg flex items-center justify-center transition-all group-hover:scale-105">
            <span className="text-primary-foreground font-bold text-sm">RS</span>
          </div>
        )}
        <span className="text-lg font-bold text-foreground">{brand.title}</span>
      </Link>

      {/* Mobile Search and Menu */}
      <div className="flex items-center gap-2">
        {search && (
          <Button variant="ghost" size="icon" className="md:hidden">
            <Search className="h-5 w-5" />
            <span className="sr-only">Search</span>
          </Button>
        )}

        {/* Mobile Menu */}
        <Sheet>
          <SheetTrigger asChild>
            <Button variant="ghost" size="icon">
              <Menu className="h-5 w-5" />
              <span className="sr-only">Toggle menu</span>
            </Button>
          </SheetTrigger>
          <SheetContent side="right" className="w-80 overflow-y-auto">
            <SheetHeader className="border-b pb-4">
              <SheetTitle>
                <Link to={brand.url} className="flex items-center gap-2">
                  {brand.logo ? (
                    <img src={brand.logo} alt={brand.title} className="h-8 w-8" />
                  ) : (
                    <div className="h-8 w-8 bg-primary rounded-lg flex items-center justify-center">
                      <span className="text-primary-foreground font-bold text-sm">RS</span>
                    </div>
                  )}
                  <span className="text-lg font-bold">{brand.title}</span>
                </Link>
              </SheetTitle>
            </SheetHeader>

            {/* Mobile Search */}
            {search && (
              <div className="py-4 border-b">
                <SearchBar {...search} className="w-full" />
              </div>
            )}

            <div className="mt-6 flex flex-col gap-4">
              <Accordion type="single" collapsible className="w-full">
                {navigation.map((item, i) => {
                  if (item.children && item.children.length > 0) {
                    return (
                      <AccordionItem
                        key={`${item.title}-${i}`}
                        value={item.title}
                        className="border-b-0"
                      >
                        <AccordionTrigger className="py-3 font-semibold hover:no-underline text-left">
                          <span className="flex items-center">
                            {item.icon && <span className="mr-2">{item.icon}</span>}
                            {item.title}
                          </span>
                        </AccordionTrigger>
                        <AccordionContent className="pb-2">
                          {item.children.map((child, ii) => (
                            <Link
                              key={ii}
                              to={child.url}
                              target={child.target}
                              onClick={onClose}
                              className="flex select-none gap-3 rounded-md p-3 leading-none outline-none transition-colors hover:bg-accent hover:text-accent-foreground"
                            >
                              {child.icon && <span className="mt-1 shrink-0">{child.icon}</span>}
                              <div>
                                <div className="text-sm font-semibold">{child.title}</div>
                                {child.description && (
                                  <p className="text-sm leading-snug text-muted-foreground">
                                    {child.description}
                                  </p>
                                )}
                              </div>
                            </Link>
                          ))}
                        </AccordionContent>
                      </AccordionItem>
                    );
                  }

                  return (
                    <Link
                      key={i}
                      to={item.url}
                      target={item.target}
                      onClick={onClose}
                      className="flex items-center gap-2 py-3 font-semibold hover:text-primary transition-colors"
                    >
                      {item.icon && <span>{item.icon}</span>}
                      {item.title}
                    </Link>
                  );
                })}
              </Accordion>

              <div className="border-t pt-6 mt-4">
                {/* Mobile User Account or Auth Buttons */}
                {userAccount ? (
                  <div className="mb-6">
                    <UserAccountMenu {...userAccount} />
                  </div>
                ) : (
                  <div className="flex flex-col gap-3 mb-6">
                    {buttons.map((button, i) => (
                      <Button key={i} variant={button.variant} asChild className="w-full">
                        <Link to={button.url} target={button.target} onClick={onClose}>
                          {button.title}
                        </Link>
                      </Button>
                    ))}
                  </div>
                )}

                <div className="flex items-center justify-between pt-4 border-t">
                  {/* Theme and language controls can be added here if needed */}
                </div>
              </div>
            </div>
          </SheetContent>
        </Sheet>
      </div>
    </div>
  );
}
