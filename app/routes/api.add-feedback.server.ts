import type { ActionFunctionArgs } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";
import { respData, respErr } from "~/lib/api/resp";
import { getUser } from "~/lib/auth/middleware.server";
import { createDbFromEnv } from "~/lib/db";
import {
  createFeedback,
  type FeedbackPriority,
  type FeedbackType,
} from "~/services/feedback.server";

// Input validation schema
interface FeedbackInput {
  content?: string;
  rating?: number;
  category?: string;
  metadata?: Record<string, any>;
}

function validateFeedbackInput(input: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!input.content || typeof input.content !== "string") {
    errors.push("Content is required and must be a string");
  } else if (input.content.trim().length < 5) {
    errors.push("Content must be at least 5 characters long");
  } else if (input.content.length > 2000) {
    errors.push("Content must be less than 2000 characters");
  }

  if (input.rating !== undefined) {
    if (typeof input.rating !== "number" || input.rating < 1 || input.rating > 5) {
      errors.push("Rating must be a number between 1 and 5");
    }
  }

  if (input.category && typeof input.category !== "string") {
    errors.push("Category must be a string");
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

export async function action({ request, context }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return json({ success: false, error: "Method not allowed" }, { status: 405 });
  }

  try {
    // Enhanced database connection
    const db = context.db || createDbFromEnv(context.cloudflare?.env);
    if (!db) {
      return respErr("Database connection failed");
    }

    // Parse and validate request body
    let body: FeedbackInput;
    try {
      body = await request.json();
    } catch (error) {
      return json({ success: false, error: "Invalid JSON in request body" }, { status: 400 });
    }

    // Validate input
    const validation = validateFeedbackInput(body);
    if (!validation.isValid) {
      return json(
        {
          success: false,
          error: "Validation failed",
          details: validation.errors,
        },
        { status: 400 }
      );
    }

    const { content, rating, category, metadata } = body;
    const userResult = await getUser(request);
    const user = userResult.success ? userResult.user : null;

    // Create feedback object with enhanced data
    const feedback: Feedback = {
      user_uuid: user?.id,
      content: content!.trim(),
      rating: rating,
      created_at: getIsoTimestr(),
      status: "created",
      // Add additional fields if your schema supports them
      ...(category && { category }),
      ...(metadata && { metadata: JSON.stringify(metadata) }),
    };

    // Insert feedback with transaction support
    const savedFeedback = await insertFeedback(feedback, db);

    return respData({
      feedback: savedFeedback,
      message: "Feedback submitted successfully",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Add feedback failed:", error);

    // Provide more specific error messages
    if (error instanceof Error) {
      if (error.message.includes("duplicate")) {
        return respErr("Duplicate feedback detected");
      }
      if (error.message.includes("constraint")) {
        return respErr("Invalid data provided");
      }
    }

    return respErr(error instanceof Error ? error.message : "Failed to add feedback");
  }
}
