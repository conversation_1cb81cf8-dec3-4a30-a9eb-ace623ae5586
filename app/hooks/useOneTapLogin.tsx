import type { CredentialResponse } from "google-one-tap"; // Using the types defined in global.d.ts
// app/hooks/useOneTapLogin.tsx
import { useCallback, useEffect, useState } from "react";

// Mock NextAuth functionality for Remix environment
const mockUseSession = () => ({
  data: null,
  status: "unauthenticated" as const,
});

const mockSignIn = async (provider: string, options: any) => {
  console.log("Mock signIn called with:", provider, options);
  return { ok: true, error: null };
};

interface UseOneTapLoginOptions {
  clientId: string | undefined;
  oneTapEnabled: boolean;
  promptParentId?: string; // Optional: for custom prompt positioning
  onLoginSuccess?: () => void; // Optional callback for successful login
  onLoginError?: (error: Error) => void; // Optional callback for login error
}

interface UseOneTapLoginReturn {
  isLoading: boolean;
  error: Error | null;
  isOneTapInitialized: boolean;
}

// Export types for use in other files
export type { UseOneTapLoginOptions, UseOneTapLoginReturn };

export const useOneTapLogin = ({
  clientId,
  oneTapEnabled,
  promptParentId,
  onLoginSuccess,
  onLoginError,
}: UseOneTapLoginOptions): UseOneTapLoginReturn => {
  // Use mock session for Remix environment
  const { data: session, status } = mockUseSession();

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [isOneTapInitialized, setIsOneTapInitialized] = useState(false);
  const [retryTimer, setRetryTimer] = useState<NodeJS.Timeout | null>(null);

  const handleCredentialResponse = useCallback(
    async (response: CredentialResponse) => {
      if (response.credential) {
        setIsLoading(true);
        setError(null);
        try {
          console.log("One Tap: Credential received, attempting to sign in...");

          // Use mock signIn for Remix environment
          const signInResponse = await mockSignIn("google-one-tap", {
            credential: response.credential,
            redirect: false, // Handle redirect manually or based on response
          });

          if (signInResponse?.error) {
            console.error("One Tap: Sign-in error:", signInResponse.error);
            throw new Error(signInResponse.error);
          }

          if (signInResponse?.ok && !signInResponse.error) {
            console.log("One Tap: Sign-in successful.");
            if (onLoginSuccess) onLoginSuccess();
          } else {
            // This case should ideally be caught by signInResponse.error, but as a fallback:
            console.error("One Tap: Sign-in failed with status:", signInResponse?.status);
            throw new Error(signInResponse?.error || "Sign-in failed due to an unknown error.");
          }
        } catch (e: any) {
          console.error("One Tap: Exception during sign-in process:", e);
          setError(e);
          if (onLoginError) onLoginError(e);
        } finally {
          setIsLoading(false);
        }
      } else {
        console.warn("One Tap: No credential in response.", response);
        setError(new Error("No credential received from Google One Tap."));
        if (onLoginError) onLoginError(new Error("No credential received."));
      }
    },
    [onLoginSuccess, onLoginError]
  );

  const initializeGoogleOneTap = useCallback(() => {
    if (window.google?.accounts?.id && clientId && !isOneTapInitialized) {
      try {
        console.log("One Tap: Initializing Google Accounts ID with client ID:", clientId);
        window.google.accounts.id.initialize({
          client_id: clientId,
          callback: handleCredentialResponse,
          auto_select: false, // Set to true for auto-selection if desired, false requires user click
          cancel_on_tap_outside: true,
          prompt_parent_id: promptParentId,
          ux_mode: "popup", // Or 'redirect'
          // itp_support: true, // Consider for Safari ITP
          // use_fedcm_for_prompt: true, // For future FedCM compatibility
        });
        setIsOneTapInitialized(true);
        console.log("One Tap: Google Accounts ID initialized.");
      } catch (initError) {
        console.error("One Tap: Error initializing Google Accounts ID:", initError);
        setError(new Error("Failed to initialize Google One Tap."));
      }
    } else if (!clientId) {
      console.warn("One Tap: Google Client ID is missing. One Tap will not be initialized.");
    } else if (isOneTapInitialized) {
      // console.log('One Tap: Already initialized.');
    } else {
      // console.log('One Tap: Google library not yet available.');
    }
  }, [clientId, handleCredentialResponse, promptParentId, isOneTapInitialized]);

  const displayPrompt = useCallback(() => {
    if (isOneTapInitialized && status === "unauthenticated" && !isLoading) {
      console.log("One Tap: Displaying prompt for unauthenticated user.");
      try {
        window.google.accounts.id.prompt((notification) => {
          // Handle prompt notifications (e.g., display, skip, dismiss)
          if (notification.isNotDisplayed()) {
            console.warn(
              `One Tap: Prompt not displayed. Reason: ${notification.getNotDisplayedReason()}`
            );
            // Implement retry logic as per issue description (3-second timer)
            if (retryTimer) clearTimeout(retryTimer); // Clear existing timer
            const timer = setTimeout(() => {
              console.log("One Tap: Retrying prompt display after 3 seconds.");
              displayPrompt(); // Retry displaying
            }, 3000);
            setRetryTimer(timer);
          } else if (notification.isSkippedMoment()) {
            console.log(`One Tap: Prompt skipped. Reason: ${notification.getSkippedReason()}`);
          } else if (notification.isDismissedReason()) {
            console.log(`One Tap: Prompt dismissed. Reason: ${notification.getDismissedReason()}`);
          } else if (notification.isDisplayed()) {
            console.log("One Tap: Prompt is displayed.");
          }
        });
      } catch (promptError) {
        console.error("One Tap: Error displaying prompt:", promptError);
        setError(new Error("Failed to display Google One Tap prompt."));
      }
    } else if (status === "authenticated") {
      // console.log("One Tap: User is already authenticated.");
      if (retryTimer) clearTimeout(retryTimer); // Clear timer if user authenticates
    } else if (!isOneTapInitialized) {
      // console.log("One Tap: Not initialized yet, cannot display prompt.");
    }
  }, [isOneTapInitialized, status, isLoading, retryTimer]); // Removed displayPrompt from dependency array

  // Effect to load Google Identity Services (GIS) library
  useEffect(() => {
    if (!oneTapEnabled) {
      console.log("One Tap: Feature is disabled via environment variable.");
      return;
    }
    if (document.getElementById("google-identity-services-script")) {
      // console.log('One Tap: GIS script already present.');
      initializeGoogleOneTap(); // Initialize if script is already there
      return;
    }

    console.log("One Tap: Loading Google Identity Services library...");
    const script = document.createElement("script");
    script.id = "google-identity-services-script";
    script.src = "https://accounts.google.com/gsi/client";
    script.async = true;
    script.defer = true;
    script.onload = () => {
      console.log("One Tap: Google Identity Services library loaded.");
      initializeGoogleOneTap();
    };
    script.onerror = () => {
      console.error("One Tap: Failed to load Google Identity Services library.");
      setError(new Error("Failed to load Google GIS library."));
    };
    document.head.appendChild(script);

    return () => {
      // Cleanup script if component unmounts, though often it's fine to leave it.
      // const existingScript = document.getElementById('google-identity-services-script');
      // if (existingScript) document.head.removeChild(existingScript);
      if (retryTimer) clearTimeout(retryTimer);
    };
  }, [oneTapEnabled, initializeGoogleOneTap, retryTimer]); // Added retryTimer to cleanup

  // Effect to display the prompt when conditions are met
  useEffect(() => {
    if (oneTapEnabled && isOneTapInitialized && status === "unauthenticated" && !isLoading) {
      // Initial prompt display attempt
      displayPrompt();
    }
    // Clear timer on unmount or if status changes from unauthenticated
    return () => {
      if (retryTimer) clearTimeout(retryTimer);
    };
  }, [oneTapEnabled, isOneTapInitialized, status, isLoading, displayPrompt, retryTimer]);

  return { isLoading, error, isOneTapInitialized };
};
