import { <PERSON> } from "@remix-run/react";
import {
  Book<PERSON>pen,
  <PERSON>t,
  Code,
  CreditCard,
  Edit3,
  MessageSquare,
  Settings,
  Sparkles,
  Users,
} from "lucide-react";
import { <PERSON><PERSON>, buttonVariants } from "~/components/ui/button";
import { cn } from "~/lib/utils/utils";
import { Brand } from "./brand";
import { MobileMenu } from "./mobile-menu";
import { NavigationMenuComponent } from "./navigation-menu";
import { SearchBar } from "./search-bar";
import { TrustSignals } from "./trust-signals";
import type { ActionButton, HeaderProps, NavItem } from "./types";
import { UserAccountMenu } from "./user-account-menu";

const defaultNavigation: NavItem[] = [
  {
    title: "Features",
    url: "#",
    children: [
      {
        title: "AI Tools",
        url: "/dev/ai-tools",
        description: "Comprehensive AI tools for text generation and image creation",
        icon: <MessageSquare className="h-4 w-4" />,
      },
      {
        title: "Cloudflare AI",
        url: "/dev/cloudflare-ai",
        description: "Edge AI computing with Cloudflare Workers AI",
        icon: <Bot className="h-4 w-4" />,
      },
      {
        title: "Components",
        url: "/dev/components",
        description: "Beautiful UI components built with Radix and Tailwind",
        icon: <Sparkles className="h-4 w-4" />,
      },
      {
        title: "Analytics",
        url: "/dev/performance",
        description: "Performance monitoring and analytics dashboard",
        icon: <Settings className="h-4 w-4" />,
      },
    ],
  },
  {
    title: "Developer",
    url: "#",
    children: [
      {
        title: "Developer Center",
        url: "/dev/center",
        description: "Development tools and test page navigation center",
        icon: <Code className="h-4 w-4" />,
      },
      {
        title: "Development Test",
        url: "/dev-test",
        description: "Unified functional testing page",
        icon: <Settings className="h-4 w-4" />,
      },
      {
        title: "Language Switcher",
        url: "/language-demo",
        description: "Language switcher component showcase",
        icon: <Sparkles className="h-4 w-4" />,
      },
      {
        title: "Content Management",
        url: "/keystatic",
        description: "Manage blog posts, pages, and site content",
        icon: <Edit3 className="h-4 w-4" />,
      },
    ],
  },
  {
    title: "Pricing",
    url: "/pricing",
    icon: <CreditCard className="h-4 w-4" />,
  },
  {
    title: "Blog",
    url: "/blog",
    icon: <BookOpen className="h-4 w-4" />,
  },
  {
    title: "About",
    url: "/about",
    icon: <Users className="h-4 w-4" />,
  },
];

const defaultButtons: ActionButton[] = [
  {
    title: "Get Started",
    url: "/",
    variant: "default",
  },
];

export default function Header({
  brand = {
    title: "Remix Starter",
    url: "/",
  },
  navigation = defaultNavigation,
  buttons = defaultButtons,
  search,
  userAccount,
  trustSignals,
}: HeaderProps) {
  return (
    <header
      className="sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur-xl supports-[backdrop-filter]:bg-background/80 transition-all duration-300"
      aria-label="Main navigation"
    >
      {/* Trust Signals Bar */}
      {trustSignals && (
        <div
          className="border-b border-border/20 bg-muted/30"
          role="complementary"
          aria-label="Trust and security information"
        >
          <div className="container mx-auto px-4 py-2">
            <TrustSignals {...trustSignals} />
          </div>
        </div>
      )}

      <div className="container mx-auto px-4">
        <div className="flex h-16 lg:h-20 items-center justify-between">
          {/* Desktop Navigation */}
          <div className="hidden lg:flex lg:items-center lg:gap-8">
            {/* Brand */}
            <Brand {...brand} />

            {/* Navigation Menu */}
            <NavigationMenuComponent items={navigation} />
          </div>

          {/* Center Search (Desktop) */}
          <div
            className="hidden lg:flex flex-1 justify-center max-w-md mx-8"
            aria-label="Site search"
          >
            {search && <SearchBar {...search} />}
          </div>

          {/* Desktop Actions */}
          <div className="hidden lg:flex lg:items-center lg:gap-3 shrink-0">
            {/* User Account or Auth Buttons */}
            {userAccount ? (
              <UserAccountMenu {...userAccount} />
            ) : (
              buttons.map((button, i) => (
                <Button
                  key={`${button.title}-${i}`}
                  variant={button.variant}
                  asChild
                  className={cn(
                    "transition-all duration-200",
                    i === 0 && "bg-primary hover:bg-primary/90 text-primary-foreground font-medium"
                  )}
                >
                  <Link to={button.url} target={button.target} className="flex items-center gap-2">
                    {button.title}
                  </Link>
                </Button>
              ))
            )}
          </div>

          {/* Mobile Navigation */}
          <MobileMenu
            brand={brand}
            navigation={navigation}
            buttons={buttons}
            search={search}
            userAccount={userAccount}
          />
        </div>
      </div>
    </header>
  );
}

// Re-export types for convenience
export type {
  HeaderProps,
  NavItem,
  SearchProps,
  TrustSignalsProps,
  UserAccountProps,
} from "./types";
