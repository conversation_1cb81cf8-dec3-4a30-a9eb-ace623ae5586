import { Link } from "@remix-run/react";
import { Button } from "~/components/ui/button";
import ContentSection from "./content-section";

interface CTAButton {
  title: string;
  url: string;
  variant?: "default" | "outline" | "secondary" | "ghost" | "link" | "destructive";
}

interface CTAProps {
  title: string;
  description: string;
  buttons: CTAButton[];
}

export default function CTA({ title, description, buttons }: CTAProps) {
  return (
    <ContentSection
      title={title}
      description={description}
      background="cta"
      decorations={true}
      padding="lg"
      headerSpacing="md"
      maxWidth="6xl"
    >
      {/* Buttons */}
      <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
        {buttons.map((button, index) => (
          <Button
            key={`${button.title}-${index}`}
            asChild
            size="lg"
            variant={index === 0 ? "default" : button.variant || "outline"}
            className={`px-8 py-3 text-base font-medium transition-all duration-200 ${
              index === 0 ? "bg-primary hover:bg-primary/90 text-primary-foreground" : ""
            }`}
          >
            <Link to={button.url}>{button.title}</Link>
          </Button>
        ))}
      </div>

      {/* Additional info */}
      <div className="text-center">
        <p className="text-sm text-muted-foreground mb-6">
          Join thousands of developers building amazing applications
        </p>

        {/* Trust indicators */}
        <div className="flex items-center justify-center gap-6 text-sm text-muted-foreground flex-wrap">
          <div className="flex items-center gap-2">
            <svg
              className="w-4 h-4 text-success"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 13l4 4L19 7"
              />
            </svg>
            <span>No credit card required</span>
          </div>
          <div className="flex items-center gap-2">
            <svg
              className="w-4 h-4 text-success"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
              />
            </svg>
            <span>Enterprise security</span>
          </div>
        </div>
      </div>
    </ContentSection>
  );
}
