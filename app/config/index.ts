// Centralized configuration exports
// This file provides a single entry point for all configuration

export { <PERSON><PERSON><PERSON><PERSON>, Theme, type ThemeType } from "./constants";
export {
  animations,
  borderRadius,
  breakpoints as designBreakpoints,
  colors,
  componentVariants,
  designSystem,
  designSystem as design,
  layouts,
  shadows,
  spacing as designSpacing,
  typography,
  utils,
  uxPatterns,
} from "./design-system";
// Export specific items to avoid naming conflicts
// Re-export commonly used configs with aliases for convenience
export {
  type FooterLink,
  type FooterLinkSection,
  footerLinks,
  getAnalyticsProps,
  isExternalUrl,
  type SocialPlatform,
  siteConfig,
  siteConfig as site,
} from "./footer.config";

export {
  getLandingPageConfig,
  type LandingPageConfig,
} from "./landing";
export {
  getCanonicalUrl,
  getOrganizationStructuredData,
  getPageConfig,
  getWebsiteStructuredData,
  pageConfigs,
  siteConfig as seoSiteConfig,
} from "./seo";
export {
  animationConfig,
  breakpoints as uiBreakpoints,
  componentDefaults,
  default as uiConfig,
  spacing as uiSpacing,
  themeConfig,
  zIndex,
} from "./ui";

// Environment-specific configurations (safe for client-side and Cloudflare Workers)
export const env = {
  isDevelopment:
    typeof process !== "undefined" && process.env ? process.env.NODE_ENV === "development" : false,
  isProduction:
    typeof process !== "undefined" && process.env ? process.env.NODE_ENV === "production" : true,
  isTest: typeof process !== "undefined" && process.env ? process.env.NODE_ENV === "test" : false,
} as const;

// API endpoints configuration
export const apiConfig = {
  baseUrl: "/api", // Use static value for client-side
  timeout: 10000,
  retries: 3,
} as const;

// Feature flags with safe defaults for client-side
export const featureFlags = {
  enableAnalytics: false, // Default to false for client-side
  enableNewsletter: false,
  enableBlog: true, // Default to true
  enableResources: false,
  enableStatus: false,
} as const;
