/**
 * R2 File Download API - Simplified Version
 * Direct use of Cloudflare Workers R2 binding
 */

import type { LoaderFunctionArgs } from "@remix-run/cloudflare";

export async function loader({ request, context }: LoaderFunctionArgs) {
  try {
    // Direct use of R2 binding
    const R2_BUCKET = context.cloudflare.env.R2_BUCKET;
    if (!R2_BUCKET) {
      return new Response("R2 bucket not configured", { status: 503 });
    }

    const url = new URL(request.url);
    const key = url.searchParams.get("key");

    if (!key) {
      return new Response("Missing file key parameter", { status: 400 });
    }

    // Get file from R2
    const object = await R2_BUCKET.get(key);

    if (!object) {
      return new Response("File not found", { status: 404 });
    }

    // Prepare response headers
    const headers = new Headers();

    // Set content type
    if (object.httpMetadata?.contentType) {
      headers.set("Content-Type", object.httpMetadata.contentType);
    }

    // Set content length
    headers.set("Content-Length", object.size.toString());

    // Set cache headers
    headers.set("Cache-Control", "public, max-age=31536000");

    // Set ETag
    headers.set("ETag", object.etag);

    // Set last modified time
    headers.set("Last-Modified", object.uploaded.toUTCString());

    // Handle conditional requests
    const ifNoneMatch = request.headers.get("if-none-match");
    if (ifNoneMatch === object.etag) {
      return new Response(null, { status: 304 }); // Not Modified
    }

    // Set content disposition
    const disposition = url.searchParams.get("disposition");
    if (disposition === "attachment") {
      const filename = url.searchParams.get("filename") || key.split("/").pop() || "download";
      headers.set("Content-Disposition", `attachment; filename="${filename}"`);
    } else {
      headers.set("Content-Disposition", "inline");
    }

    // Add CORS headers
    headers.set("Access-Control-Allow-Origin", "*");
    headers.set("Access-Control-Allow-Methods", "GET, HEAD, OPTIONS");

    return new Response(object.body, {
      status: 200,
      headers,
    });
  } catch (error) {
    console.error("R2 download error:", error);
    return new Response(
      `Download failed: ${error instanceof Error ? error.message : "Unknown error"}`,
      { status: 500 }
    );
  }
}
