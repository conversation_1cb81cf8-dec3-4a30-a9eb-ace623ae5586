/**
 * Performance monitoring dashboard component
 */

import { useEffect, useState } from "react";
import { Badge } from "~/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { getPerformanceMetrics, type PerformanceMetrics } from "~/lib/monitoring/performance";

interface PerformanceStats {
  lcp?: number;
  fid?: number;
  cls?: number;
  fcp?: number;
  ttfb?: number;
  domReady?: number;
  loadComplete?: number;
}

export function PerformanceDashboard() {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({});
  const [stats, setStats] = useState<PerformanceStats>({});
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);

    if (typeof window === "undefined") return;

    // Get initial metrics
    const initialMetrics = getPerformanceMetrics();
    setMetrics(initialMetrics);

    // Calculate navigation timing stats
    if (initialMetrics.navigationTiming) {
      const nav = initialMetrics.navigationTiming;
      setStats({
        ttfb: nav.responseStart - nav.requestStart,
        domReady: nav.domContentLoadedEventEnd - nav.navigationStart,
        loadComplete: nav.loadEventEnd - nav.navigationStart,
      });
    }

    // Listen for Web Vitals
    const handleWebVitals = () => {
      // This would be populated by the performance monitoring system
      // For demo purposes, we'll simulate some values
      setTimeout(() => {
        setStats((prev) => ({
          ...prev,
          lcp: Math.random() * 3000 + 1000, // 1-4s
          fid: Math.random() * 200 + 50, // 50-250ms
          cls: Math.random() * 0.3, // 0-0.3
          fcp: Math.random() * 2000 + 800, // 0.8-2.8s
        }));
      }, 1000);
    };

    handleWebVitals();
  }, []);

  if (!isClient) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Performance Metrics</CardTitle>
          <CardDescription>Loading performance data...</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  const getScoreColor = (value: number, thresholds: { good: number; poor: number }) => {
    if (value <= thresholds.good) return "bg-green-500";
    if (value <= thresholds.poor) return "bg-yellow-500";
    return "bg-red-500";
  };

  const getScoreText = (value: number, thresholds: { good: number; poor: number }) => {
    if (value <= thresholds.good) return "Good";
    if (value <= thresholds.poor) return "Needs Improvement";
    return "Poor";
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {/* Core Web Vitals */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Core Web Vitals</CardTitle>
          <CardDescription>Key user experience metrics</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Largest Contentful Paint */}
          {stats.lcp && (
            <div className="flex justify-between items-center">
              <div>
                <p className="font-medium">LCP</p>
                <p className="text-sm text-gray-600">{Math.round(stats.lcp)}ms</p>
              </div>
              <Badge
                className={`text-white ${getScoreColor(stats.lcp, { good: 2500, poor: 4000 })}`}
              >
                {getScoreText(stats.lcp, { good: 2500, poor: 4000 })}
              </Badge>
            </div>
          )}

          {/* First Input Delay */}
          {stats.fid && (
            <div className="flex justify-between items-center">
              <div>
                <p className="font-medium">FID</p>
                <p className="text-sm text-gray-600">{Math.round(stats.fid)}ms</p>
              </div>
              <Badge className={`text-white ${getScoreColor(stats.fid, { good: 100, poor: 300 })}`}>
                {getScoreText(stats.fid, { good: 100, poor: 300 })}
              </Badge>
            </div>
          )}

          {/* Cumulative Layout Shift */}
          {stats.cls && (
            <div className="flex justify-between items-center">
              <div>
                <p className="font-medium">CLS</p>
                <p className="text-sm text-gray-600">{stats.cls.toFixed(3)}</p>
              </div>
              <Badge
                className={`text-white ${getScoreColor(stats.cls, { good: 0.1, poor: 0.25 })}`}
              >
                {getScoreText(stats.cls, { good: 0.1, poor: 0.25 })}
              </Badge>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Loading Performance */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Loading Performance</CardTitle>
          <CardDescription>Page loading metrics</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* First Contentful Paint */}
          {stats.fcp && (
            <div className="flex justify-between items-center">
              <div>
                <p className="font-medium">FCP</p>
                <p className="text-sm text-gray-600">{Math.round(stats.fcp)}ms</p>
              </div>
              <Badge
                className={`text-white ${getScoreColor(stats.fcp, { good: 1800, poor: 3000 })}`}
              >
                {getScoreText(stats.fcp, { good: 1800, poor: 3000 })}
              </Badge>
            </div>
          )}

          {/* Time to First Byte */}
          {stats.ttfb && (
            <div className="flex justify-between items-center">
              <div>
                <p className="font-medium">TTFB</p>
                <p className="text-sm text-gray-600">{Math.round(stats.ttfb)}ms</p>
              </div>
              <Badge
                className={`text-white ${getScoreColor(stats.ttfb, { good: 800, poor: 1800 })}`}
              >
                {getScoreText(stats.ttfb, { good: 800, poor: 1800 })}
              </Badge>
            </div>
          )}

          {/* DOM Ready */}
          {stats.domReady && (
            <div className="flex justify-between items-center">
              <div>
                <p className="font-medium">DOM Ready</p>
                <p className="text-sm text-gray-600">{Math.round(stats.domReady)}ms</p>
              </div>
              <Badge variant="outline">
                {stats.domReady < 1500 ? "Fast" : stats.domReady < 3000 ? "Average" : "Slow"}
              </Badge>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Resource Performance */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Resource Loading</CardTitle>
          <CardDescription>Asset loading performance</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {metrics.resourceTiming && (
            <>
              <div className="flex justify-between items-center">
                <div>
                  <p className="font-medium">Total Resources</p>
                  <p className="text-sm text-gray-600">Loaded assets</p>
                </div>
                <Badge variant="outline">{metrics.resourceTiming.length}</Badge>
              </div>

              <div className="flex justify-between items-center">
                <div>
                  <p className="font-medium">Slow Resources</p>
                  <p className="text-sm text-gray-600">&gt;1s load time</p>
                </div>
                <Badge variant="outline">
                  {metrics.resourceTiming.filter((r) => r.duration > 1000).length}
                </Badge>
              </div>

              <div className="flex justify-between items-center">
                <div>
                  <p className="font-medium">Images</p>
                  <p className="text-sm text-gray-600">Image resources</p>
                </div>
                <Badge variant="outline">
                  {
                    metrics.resourceTiming.filter((r) =>
                      r.name.match(/\.(jpg|jpeg|png|gif|webp|svg)$/i)
                    ).length
                  }
                </Badge>
              </div>
            </>
          )}

          {/* Load Complete */}
          {stats.loadComplete && (
            <div className="flex justify-between items-center">
              <div>
                <p className="font-medium">Load Complete</p>
                <p className="text-sm text-gray-600">{Math.round(stats.loadComplete)}ms</p>
              </div>
              <Badge variant="outline">
                {stats.loadComplete < 2000
                  ? "Fast"
                  : stats.loadComplete < 4000
                    ? "Average"
                    : "Slow"}
              </Badge>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * Simple performance indicator for header/footer
 */
export function PerformanceIndicator() {
  const [score, setScore] = useState<number | null>(null);

  useEffect(() => {
    if (typeof window === "undefined") return;

    // Calculate a simple performance score
    setTimeout(() => {
      const navigation = performance.getEntriesByType(
        "navigation"
      )[0] as PerformanceNavigationTiming;
      if (navigation) {
        const loadTime = navigation.loadEventEnd - navigation.navigationStart;
        const score = Math.max(0, 100 - Math.floor(loadTime / 50)); // Simple scoring
        setScore(Math.min(100, score));
      }
    }, 1000);
  }, []);

  if (score === null) return null;

  const getScoreColor = (score: number) => {
    if (score >= 90) return "text-green-600";
    if (score >= 70) return "text-yellow-600";
    return "text-red-600";
  };

  return (
    <div className="flex items-center space-x-2 text-sm">
      <span className="text-gray-600">Perf:</span>
      <span className={`font-medium ${getScoreColor(score)}`}>{score}</span>
    </div>
  );
}
