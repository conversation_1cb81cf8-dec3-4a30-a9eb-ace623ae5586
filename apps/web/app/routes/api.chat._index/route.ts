import { ActionFunctionArgs } from '@vercel/remix';
import { llmService } from '@kit/ai';
import { StreamingTextResponse } from 'ai';

export const action = async ({ request }: ActionFunctionArgs) => {
  try {
    const body = await request.json();
    const { messages } = body;

    // 创建一个 ReadableStream
    const stream = new ReadableStream({
      async start(controller) {
        try {
          // 确保使用正确的模型名称
          for await (const chunk of llmService.streamChatCompletion(messages, {
            model: 'deepseek-chat',  // 使用正确的模型名称
            temperature: 0.7,
            systemPrompt: "You are a helpful assistant."  // 添加系统提示
          })) {
            controller.enqueue(chunk);
          }
          controller.close();
        } catch (error) {
          controller.error(error);
        }
      },
    });

    // 返回流式响应
    return new StreamingTextResponse(stream);
  } catch (error) {
    console.error('Chat API error:', error);
    return new Response(JSON.stringify({ error: 'Chat completion failed' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
};