/**
 * Clean Design System for US and Japanese Users
 *
 * This file contains design tokens optimized for clean, minimal aesthetics
 * preferred by both US and Japanese users.
 */

// Clean color palette for US and Japanese users
export const colors = {
  // Primary - clean blue
  primary: {
    50: "#eff6ff",
    100: "#dbeafe",
    500: "#0066ff",
    600: "#0052cc",
    700: "#003d99",
    900: "#002966",
  },

  // Neutral grays - minimal and clean
  gray: {
    50: "#fafafa",
    100: "#f5f5f5",
    200: "#e5e5e5",
    300: "#d4d4d4",
    400: "#a3a3a3",
    500: "#737373",
    600: "#525252",
    700: "#404040",
    800: "#262626",
    900: "#171717",
  },

  // System colors
  success: "#22c55e",
  warning: "#f59e0b",
  error: "#ef4444",
} as const;

// Clean typography scale
export const typography = {
  display: {
    fontSize: "2.5rem",
    fontWeight: "700",
    lineHeight: "1.2",
    letterSpacing: "-0.025em",
  },
  headline: {
    fontSize: "2rem",
    fontWeight: "600",
    lineHeight: "1.3",
    letterSpacing: "-0.015em",
  },
  title: {
    fontSize: "1.5rem",
    fontWeight: "600",
    lineHeight: "1.4",
  },
  body: {
    fontSize: "1rem",
    fontWeight: "400",
    lineHeight: "1.5",
  },
  caption: {
    fontSize: "0.875rem",
    fontWeight: "500",
    lineHeight: "1.4",
  },
} as const;

// Spacing scale for consistent layouts
export const spacing = {
  xs: "0.5rem",
  sm: "1rem",
  md: "1.5rem",
  lg: "2rem",
  xl: "3rem",
  "2xl": "4rem",
  "3xl": "6rem",
  "4xl": "8rem",
} as const;

// Clean border radius
export const borderRadius = {
  sm: "0.25rem",
  md: "0.5rem",
  lg: "0.5rem",
  xl: "0.75rem",
  "2xl": "1rem",
  full: "9999px",
} as const;

// Subtle shadow system
export const shadows = {
  sm: "0 1px 2px 0 rgb(0 0 0 / 0.03)",
  md: "0 4px 8px 0 rgb(0 0 0 / 0.05)",
  lg: "0 8px 16px 0 rgb(0 0 0 / 0.08)",
  xl: "0 12px 24px 0 rgb(0 0 0 / 0.10)",
  "2xl": "0 16px 32px 0 rgb(0 0 0 / 0.12)",
} as const;

// Animation durations
export const animations = {
  fast: "150ms",
  normal: "300ms",
  slow: "500ms",
} as const;

// Clean component variants
export const componentVariants = {
  button: {
    primary:
      "bg-primary text-primary-foreground font-medium px-6 py-3 rounded-lg transition-all duration-200 hover:bg-primary/90 shadow-sm hover:shadow-md",
    secondary:
      "bg-background border border-border text-foreground font-medium px-6 py-3 rounded-lg transition-all duration-200 hover:bg-accent shadow-sm hover:shadow-md",
    outline:
      "border border-primary text-primary font-medium px-6 py-3 rounded-lg transition-all duration-200 hover:bg-primary/5",
  },
  card: {
    default: "card-modern p-6",
    elevated: "card-modern p-6 hover:shadow-md",
    minimal: "bg-card border border-border p-6 rounded-lg",
  },
  section: {
    default: "py-12 px-4",
    large: "py-16 px-4",
    hero: "py-20 px-4",
  },
} as const;

// Layout patterns
// Note: Container layouts are part of the design system and widely used
export const layouts = {
  container: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",
  containerSmall: "max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",
  grid: {
    cols1: "grid grid-cols-1 gap-6",
    cols2: "grid grid-cols-1 md:grid-cols-2 gap-6",
    cols3: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",
    cols4: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",
  },
  flex: {
    center: "flex items-center justify-center",
    between: "flex items-center justify-between",
    start: "flex items-center justify-start",
    column: "flex flex-col",
  },
} as const;

// Clean UX patterns
export const uxPatterns = {
  hero: {
    badge:
      "inline-flex items-center px-3 py-1 rounded-lg text-sm font-medium bg-muted text-muted-foreground",
    title: "text-display font-bold text-foreground mb-6",
    subtitle: "text-lg text-muted-foreground mb-8 max-w-2xl",
    cta: "flex flex-col sm:flex-row gap-4 justify-center items-center",
  },
  section: {
    header: "text-center mb-12",
    title: "text-headline font-bold text-foreground mb-4",
    description: "text-base text-muted-foreground max-w-2xl mx-auto",
  },
  card: {
    header: "mb-4",
    title: "text-title font-semibold text-foreground mb-2",
    description: "text-muted-foreground",
    footer: "mt-4 pt-4 border-t border-border",
  },
} as const;

// Utility functions
export const utils = {
  cn: (...classes: (string | undefined | null | false)[]): string => {
    return classes.filter(Boolean).join(" ");
  },

  // Generate consistent gradient backgrounds
  gradient: (from: string, to: string): string => {
    return `bg-gradient-to-r from-${from} to-${to}`;
  },

  // Generate clean hover effects
  hover: (scale = "102"): string => {
    return `transition-all duration-200 hover:scale-${scale} hover:shadow-md`;
  },

  // Generate consistent focus styles
  focus: (): string => {
    return "focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2";
  },
} as const;

// Responsive breakpoints
export const breakpoints = {
  sm: "640px",
  md: "768px",
  lg: "1024px",
  xl: "1280px",
  "2xl": "1536px",
} as const;

// Export everything as a single design system object
export const designSystem = {
  colors,
  typography,
  spacing,
  borderRadius,
  shadows,
  animations,
  componentVariants,
  layouts,
  uxPatterns,
  utils,
  breakpoints,
} as const;

export default designSystem;
