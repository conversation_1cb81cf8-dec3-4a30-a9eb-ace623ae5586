/**
 * Analytics API
 * Provides analytics data for users and administrators
 */

import type { LoaderFunctionArgs } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";
import { respData, respErr } from "~/lib/api/resp";
import { createDbFromEnv } from "~/lib/db";
import { type DateRange, getAdminAnalytics, getUserAnalytics } from "~/services/analytics.server";
import { getUserUuid } from "~/services/user-management.server";

export async function loader({ request, context }: LoaderFunctionArgs) {
  try {
    const db = context.db || createDbFromEnv(context.cloudflare?.env);

    const url = new URL(request.url);
    const action = url.searchParams.get("action");
    const period = url.searchParams.get("period") || "7";
    const userUuid = url.searchParams.get("user_uuid");
    const isAdmin = url.searchParams.get("admin") === "true";

    // Parse date range
    const days = parseInt(period, 10);
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    const dateRange: DateRange = { startDate, endDate };

    switch (action) {
      case "user-analytics": {
        // Get current user UUID if not provided
        let targetUserUuid = userUuid;
        if (!targetUserUuid) {
          targetUserUuid = await getUserUuid();
          if (!targetUserUuid) {
            return respErr("Authentication required");
          }
        }

        // If requesting another user's analytics, check admin permission
        const currentUserUuid = await getUserUuid();
        if (targetUserUuid !== currentUserUuid && !isAdmin) {
          // TODO: Add proper admin authentication check
          return respErr("Access denied");
        }

        const analytics = await getUserAnalytics(targetUserUuid, dateRange, db);
        return respData({
          analytics,
          period: {
            days,
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString(),
          },
        });
      }

      case "admin-analytics": {
        // TODO: Add admin authentication check

        const analytics = await getAdminAnalytics(dateRange, db);
        return respData({
          analytics,
          period: {
            days,
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString(),
          },
        });
      }

      case "user-summary": {
        const userUuid = await getUserUuid();
        if (!userUuid) {
          return respErr("Authentication required");
        }

        // Get basic user analytics for dashboard
        const analytics = await getUserAnalytics(userUuid, dateRange, db);

        return respData({
          summary: {
            totalRequests: analytics.overview.totalRequests,
            successRate: analytics.overview.successRate,
            totalCredits: analytics.overview.totalCredits,
            totalCost: analytics.overview.totalCost,
            avgResponseTime: analytics.overview.avgResponseTime,
            topProvider:
              Object.entries(analytics.usage.byProvider).sort(([, a], [, b]) => b - a)[0]?.[0] ||
              null,
            recentActivity: analytics.recentActivity.slice(0, 5),
          },
          period: {
            days,
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString(),
          },
        });
      }

      case "admin-summary": {
        // TODO: Add admin authentication check

        const analytics = await getAdminAnalytics(dateRange, db);

        return respData({
          summary: {
            totalUsers: analytics.overview.totalUsers,
            activeUsers: analytics.overview.activeUsers,
            totalRevenue: analytics.overview.totalRevenue,
            monthlyRevenue: analytics.overview.monthlyRevenue,
            totalApiCalls: analytics.overview.totalApiCalls,
            avgRevenuePerUser: analytics.overview.avgRevenuePerUser,
            userGrowth: analytics.users.userGrowth.growth,
            feedbackCount: analytics.feedback.totalFeedback,
            averageRating: analytics.feedback.averageRating,
          },
          period: {
            days,
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString(),
          },
        });
      }

      case "export": {
        const format = url.searchParams.get("format") || "json";
        const type = url.searchParams.get("type") || "user";

        if (type === "admin") {
          // TODO: Add admin authentication check
          const analytics = await getAdminAnalytics(dateRange, db);

          if (format === "csv") {
            // TODO: Implement CSV export
            return respErr("CSV export not yet implemented");
          }

          return respData({
            analytics,
            exportInfo: {
              type: "admin",
              format,
              generatedAt: new Date().toISOString(),
              period: { days, startDate: startDate.toISOString(), endDate: endDate.toISOString() },
            },
          });
        } else {
          const userUuid = await getUserUuid();
          if (!userUuid) {
            return respErr("Authentication required");
          }

          const analytics = await getUserAnalytics(userUuid, dateRange, db);

          if (format === "csv") {
            // TODO: Implement CSV export
            return respErr("CSV export not yet implemented");
          }

          return respData({
            analytics,
            exportInfo: {
              type: "user",
              format,
              generatedAt: new Date().toISOString(),
              period: { days, startDate: startDate.toISOString(), endDate: endDate.toISOString() },
            },
          });
        }
      }

      case "real-time": {
        // Get real-time metrics for the last hour
        const realTimeEndDate = new Date();
        const realTimeStartDate = new Date();
        realTimeStartDate.setHours(realTimeStartDate.getHours() - 1);
        const realTimeDateRange: DateRange = {
          startDate: realTimeStartDate,
          endDate: realTimeEndDate,
        };

        if (isAdmin) {
          // TODO: Add admin authentication check
          const analytics = await getAdminAnalytics(realTimeDateRange, db);
          return respData({
            realTime: {
              totalApiCalls: analytics.overview.totalApiCalls,
              activeUsers: analytics.overview.activeUsers,
              recentRevenue: analytics.overview.monthlyRevenue, // This would be last hour revenue
              systemHealth: "healthy", // TODO: Implement system health check
            },
            timestamp: new Date().toISOString(),
          });
        } else {
          const userUuid = await getUserUuid();
          if (!userUuid) {
            return respErr("Authentication required");
          }

          const analytics = await getUserAnalytics(userUuid, realTimeDateRange, db);
          return respData({
            realTime: {
              recentRequests: analytics.overview.totalRequests,
              recentTokens: analytics.overview.totalTokens,
              recentCredits: analytics.overview.totalCredits,
              avgResponseTime: analytics.overview.avgResponseTime,
            },
            timestamp: new Date().toISOString(),
          });
        }
      }

      case "compare": {
        // Compare two time periods
        const comparePeriod = url.searchParams.get("compare_period") || "7";
        const compareDays = parseInt(comparePeriod, 10);

        // Current period
        const currentEndDate = new Date();
        const currentStartDate = new Date();
        currentStartDate.setDate(currentStartDate.getDate() - days);

        // Previous period
        const previousEndDate = new Date(currentStartDate);
        const previousStartDate = new Date();
        previousStartDate.setDate(previousEndDate.getDate() - compareDays);

        const currentRange: DateRange = { startDate: currentStartDate, endDate: currentEndDate };
        const previousRange: DateRange = { startDate: previousStartDate, endDate: previousEndDate };

        if (isAdmin) {
          // TODO: Add admin authentication check
          const [currentAnalytics, previousAnalytics] = await Promise.all([
            getAdminAnalytics(currentRange, db),
            getAdminAnalytics(previousRange, db),
          ]);

          return respData({
            comparison: {
              current: currentAnalytics.overview,
              previous: previousAnalytics.overview,
              growth: {
                users: currentAnalytics.overview.totalUsers - previousAnalytics.overview.totalUsers,
                revenue:
                  currentAnalytics.overview.totalRevenue - previousAnalytics.overview.totalRevenue,
                apiCalls:
                  currentAnalytics.overview.totalApiCalls -
                  previousAnalytics.overview.totalApiCalls,
              },
            },
            periods: {
              current: {
                days,
                startDate: currentStartDate.toISOString(),
                endDate: currentEndDate.toISOString(),
              },
              previous: {
                days: compareDays,
                startDate: previousStartDate.toISOString(),
                endDate: previousEndDate.toISOString(),
              },
            },
          });
        } else {
          const userUuid = await getUserUuid();
          if (!userUuid) {
            return respErr("Authentication required");
          }

          const [currentAnalytics, previousAnalytics] = await Promise.all([
            getUserAnalytics(userUuid, currentRange, db),
            getUserAnalytics(userUuid, previousRange, db),
          ]);

          return respData({
            comparison: {
              current: currentAnalytics.overview,
              previous: previousAnalytics.overview,
              growth: {
                requests:
                  currentAnalytics.overview.totalRequests -
                  previousAnalytics.overview.totalRequests,
                tokens:
                  currentAnalytics.overview.totalTokens - previousAnalytics.overview.totalTokens,
                credits:
                  currentAnalytics.overview.totalCredits - previousAnalytics.overview.totalCredits,
                cost: currentAnalytics.overview.totalCost - previousAnalytics.overview.totalCost,
              },
            },
            periods: {
              current: {
                days,
                startDate: currentStartDate.toISOString(),
                endDate: currentEndDate.toISOString(),
              },
              previous: {
                days: compareDays,
                startDate: previousStartDate.toISOString(),
                endDate: previousEndDate.toISOString(),
              },
            },
          });
        }
      }

      default:
        return respErr(
          "Invalid action. Supported actions: user-analytics, admin-analytics, user-summary, admin-summary, export, real-time, compare"
        );
    }
  } catch (error) {
    console.error("Error in analytics API:", error);
    return respErr(error instanceof Error ? error.message : "Failed to process analytics request");
  }
}
