// --- <PERSON><PERSON><PERSON> ADDED COMMENT ---
// Environment Variable Access for RESEND_API_KEY:
// This configuration attempts to read RESEND_API_KEY from `process.env`.
// - For local development with <PERSON>rang<PERSON>: Ensure RESEND_API_KEY is in your .dev.vars file.
//   Wrangler automatically loads variables from .dev.vars into `process.env`.
// - For Cloudflare Worker deployment: Secrets are bound to the Worker and available on the `env`
//   object passed to the fetch handler (e.g., `fetch(request, env, ctx)`).
//   To make `process.env.RESEND_API_KEY` work directly in deployed Workers:
//   1. Your Worker setup might polyfill `process.env` using these bindings (common in some templates).
//   2. Or, you might need to explicitly make the binding available, e.g., by setting
//      `globalThis.process = { env: { RESEND_API_KEY: env.RESEND_API_KEY } };`
//      at the beginning of your worker's fetch handler if `process` is not standard.
//   A more direct Cloudflare-native approach would be to pass `env.RESEND_API_KEY`
//   during the initialization of services that depend on it, but that would require
//   changes to how the Resend client is instantiated globally.
// --- END ADDED COMMENT ---

import { z } from "zod";

const emailConfigSchema = z.object({
  RESEND_API_KEY: z.string().min(1, "Resend API key is required"),
});

const getResendApiKey = (env?: Record<string, string | undefined>) => {
  // In Cloudflare Workers, use the env parameter passed from the context
  // In development with Wrangler, process.env is available
  const apiKey =
    env?.RESEND_API_KEY ||
    (typeof process !== "undefined" && process.env ? process.env.RESEND_API_KEY : undefined);

  if (!apiKey) {
    console.warn(
      "RESEND_API_KEY is not found. " +
        "Ensure it's set in .dev.vars for local dev or as a secret in your Cloudflare Worker environment."
    );
  }
  return apiKey;
};

// Create a function to get the API key with environment context
export function createResendConfig(env?: Record<string, string | undefined>) {
  const apiKey = getResendApiKey(env);

  if (!apiKey) {
    // This warning is for visibility. The Resend client itself will throw if the key is missing on use.
    console.warn(
      "Warning: RESEND_API_KEY is not set or not accessible. " +
        "Resend client initialization will likely fail or use a non-functional client."
    );
  }

  // Validate the API key if found. This is more of a sanity check here.
  // The actual Resend client will perform the ultimate validation of the key's format/validity.
  try {
    if (apiKey) {
      // Only parse if apiKey is not undefined
      emailConfigSchema.parse({ RESEND_API_KEY: apiKey });
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error(
        "Resend API Key configuration validation error (likely too short if this triggers, but Resend handles actual validation):",
        error.issues
      );
      // Potentially throw new Error("Invalid Resend API Key configuration.");
      // For now, we let the Resend client handle the final say on the key.
    }
    // else { throw error; } // Re-throw other unexpected errors
  }

  return { resendApiKey: apiKey };
}

// For backward compatibility in development
export const resendApiKey = getResendApiKey();
