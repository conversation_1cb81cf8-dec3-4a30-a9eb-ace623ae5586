/**
 * Analytics component for Google Analytics integration
 * Inspired by ShipAny's analytics implementation but adapted for Remix + Cloudflare
 */

import { useEffect } from "react";
import { getAnalyticsConfig, initGA } from "~/lib/monitoring/analytics";

interface AnalyticsProps {
  trackingId?: string;
}

export function Analytics({ trackingId }: AnalyticsProps) {
  useEffect(() => {
    const analyticsConfig = getAnalyticsConfig();
    if (trackingId && analyticsConfig.enabled) {
      // Set the tracking ID on window for global access
      if (typeof window !== "undefined") {
        window.GA_TRACKING_ID = trackingId;
      }

      // Initialize GA
      initGA();
    }
  }, [trackingId]);

  // Only render scripts in production
  const analyticsConfig = getAnalyticsConfig();
  if (!analyticsConfig.enabled || !trackingId) {
    return null;
  }

  return (
    <>
      {/* Google Analytics Script */}
      <script async src={`https://www.googletagmanager.com/gtag/js?id=${trackingId}`} />
      <script
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}

            // Initialize default consent state for GDPR compliance
            gtag('consent', 'default', {
              'analytics_storage': 'denied',
              'ad_storage': 'denied',
              'ad_user_data': 'denied',
              'ad_personalization': 'denied',
              'wait_for_update': 500
            });

            gtag('js', new Date());
            gtag('config', '${trackingId}', { 
              'send_page_view': false,
              'custom_map': {
                'custom_parameter_1': 'user_type',
                'custom_parameter_2': 'page_category'
              }
            });
          `,
        }}
      />
    </>
  );
}

/**
 * Development Analytics component for testing
 */
export function AnalyticsDev() {
  const analyticsConfig = getAnalyticsConfig();
  if (analyticsConfig.enabled) {
    return null;
  }

  return (
    <script
      dangerouslySetInnerHTML={{
        __html: `
          // Mock gtag for development
          window.dataLayer = window.dataLayer || [];
          function gtag(){
            console.log('GA (dev):', arguments);
            dataLayer.push(arguments);
          }
          window.gtag = gtag;
        `,
      }}
    />
  );
}

/**
 * Combined Analytics component that handles both production and development
 */
export default function AnalyticsProvider({ trackingId }: AnalyticsProps) {
  return (
    <>
      <Analytics trackingId={trackingId} />
      <AnalyticsDev />
    </>
  );
}
