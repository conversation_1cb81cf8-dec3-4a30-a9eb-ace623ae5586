import { Link, useLocation } from "@remix-run/react";
import {
  BarChart3,
  Bell,
  CreditCard,
  HelpCircle,
  Home,
  Key,
  LogOut,
  Menu,
  Settings,
  ShoppingBag,
  User,
  Users,
  X,
} from "lucide-react";
import { type ReactNode, useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { cn } from "~/lib/utils/utils";

export interface ConsoleLayoutProps {
  children: ReactNode;
  className?: string;
}

interface NavItem {
  title: string;
  url: string;
  icon: ReactNode;
  badge?: string;
}

const navigation: NavItem[] = [
  {
    title: "Overview",
    url: "/console",
    icon: <BarChart3 className="h-5 w-5" />,
  },
  {
    title: "API Keys",
    url: "/console/api-keys",
    icon: <Key className="h-5 w-5" />,
  },
  {
    title: "Credits",
    url: "/console/credits",
    icon: <CreditCard className="h-5 w-5" />,
  },
  {
    title: "Usage",
    url: "/console/usage",
    icon: <BarChart3 className="h-5 w-5" />,
  },
  {
    title: "Orders",
    url: "/console/orders",
    icon: <ShoppingBag className="h-5 w-5" />,
  },
  {
    title: "Invites",
    url: "/console/invites",
    icon: <Users className="h-5 w-5" />,
    badge: "2",
  },
  {
    title: "Profile",
    url: "/console/profile",
    icon: <User className="h-5 w-5" />,
  },
  {
    title: "Settings",
    url: "/console/settings",
    icon: <Settings className="h-5 w-5" />,
  },
];

function NavLink({ item, isActive }: { item: NavItem; isActive: boolean }) {
  return (
    <Link
      to={item.url}
      className={cn(
        "flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors",
        isActive
          ? "bg-primary text-primary-foreground"
          : "text-muted-foreground hover:text-foreground hover:bg-muted"
      )}
    >
      {item.icon}
      <span className="flex-1">{item.title}</span>
      {item.badge && (
        <Badge variant={isActive ? "secondary" : "outline"} className="text-xs">
          {item.badge}
        </Badge>
      )}
    </Link>
  );
}

export default function ConsoleLayout({ children, className = "" }: ConsoleLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const location = useLocation();

  const isActive = (url: string) => {
    if (url === "/console") {
      return location.pathname === "/console";
    }
    return location.pathname.startsWith(url);
  };

  return (
    <div className={`min-h-screen bg-background ${className}`}>
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black/50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
          onKeyDown={(e) => {
            if (e.key === "Escape") {
              setSidebarOpen(false);
            }
          }}
          role="button"
          tabIndex={0}
          aria-label="Close sidebar"
        />
      )}

      {/* Sidebar */}
      <aside
        className={cn(
          "fixed inset-y-0 left-0 z-50 w-64 bg-card border-r transform transition-transform duration-200 ease-in-out lg:translate-x-0",
          sidebarOpen ? "translate-x-0" : "-translate-x-full"
        )}
      >
        <div className="flex h-full flex-col">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b">
            <Link to="/console" className="flex items-center gap-2">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                <User className="h-5 w-5 text-primary-foreground" />
              </div>
              <span className="font-bold text-lg">Console</span>
            </Link>
            <Button
              variant="ghost"
              size="icon"
              className="lg:hidden"
              onClick={() => setSidebarOpen(false)}
            >
              <X className="h-5 w-5" />
            </Button>
          </div>

          {/* User Info */}
          <div className="p-4 border-b">
            <div className="flex items-center gap-3">
              <Avatar>
                <AvatarImage src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face" />
                <AvatarFallback>JD</AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">John Doe</p>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary" className="text-xs">
                    Pro Plan
                  </Badge>
                  <span className="text-xs text-muted-foreground">2,847 credits</span>
                </div>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
            {navigation.map((item) => (
              <NavLink key={item.url} item={item} isActive={isActive(item.url)} />
            ))}
          </nav>

          {/* Footer Actions */}
          <div className="p-4 border-t space-y-2">
            <Button variant="ghost" className="w-full justify-start" asChild>
              <Link to="/help">
                <HelpCircle className="h-4 w-4 mr-3" />
                Help & Support
              </Link>
            </Button>
            <Button
              variant="ghost"
              className="w-full justify-start text-red-600 hover:text-red-600 hover:bg-red-50"
            >
              <LogOut className="h-4 w-4 mr-3" />
              Sign Out
            </Button>
          </div>
        </div>
      </aside>

      {/* Main content */}
      <div className="lg:ml-64">
        {/* Top bar */}
        <header className="sticky top-0 z-30 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b">
          <div className="flex items-center justify-between px-4 py-3">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="icon"
                className="lg:hidden"
                onClick={() => setSidebarOpen(true)}
              >
                <Menu className="h-5 w-5" />
              </Button>

              {/* Breadcrumb */}
              <nav className="flex items-center gap-2 text-sm">
                <Link to="/" className="text-muted-foreground hover:text-foreground">
                  <Home className="h-4 w-4" />
                </Link>
                <span className="text-muted-foreground">/</span>
                <span className="font-medium">Console</span>
                {location.pathname !== "/console" && (
                  <>
                    <span className="text-muted-foreground">/</span>
                    <span className="text-muted-foreground">
                      {location.pathname.split("/").pop()?.replace("-", " ")}
                    </span>
                  </>
                )}
              </nav>
            </div>

            <div className="flex items-center gap-4">
              <Button variant="ghost" size="icon">
                <Bell className="h-5 w-5" />
              </Button>
              <Button variant="outline" size="sm" asChild>
                <Link to="/ai-tools">AI Tools</Link>
              </Button>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="p-6">{children}</main>
      </div>
    </div>
  );
}
