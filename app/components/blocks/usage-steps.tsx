import ContentSection from "./content-section";

interface UsageStep {
  title: string;
  description: string;
  icon: string;
}

interface UsageStepsProps {
  title: string;
  description: string;
  items: UsageStep[];
}

export default function UsageSteps({ title, description, items }: UsageStepsProps) {
  return (
    <ContentSection
      title={title}
      description={description}
      background="default"
      decorations={true}
      padding="md"
      headerSpacing="md"
      maxWidth="6xl"
    >
      {/* Steps */}
      <div className="relative">
        {/* Enhanced Connection Line */}
        <div className="absolute left-8 top-20 bottom-20 w-1 bg-gradient-to-b from-blue-500 via-purple-500 to-pink-500 rounded-full hidden lg:block opacity-30" />
        <div className="absolute left-8 top-20 bottom-20 w-0.5 bg-gradient-to-b from-blue-400 via-purple-400 to-pink-400 rounded-full hidden lg:block" />

        <div className="space-y-16">
          {items.map((step, index) => (
            <div
              key={`${step.title.slice(0, 20)}-${index}`}
              className="relative flex items-start space-x-8 group fade-in-up"
              style={{ animationDelay: `${index * 0.2}s` }}
            >
              {/* Step Number */}
              <div className="flex-shrink-0 relative">
                <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-2xl shadow-2xl group-hover:scale-110 group-hover:shadow-blue-500/25 transition-all duration-500 relative overflow-hidden">
                  {/* Animated background */}
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-400 to-purple-500 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                  <span className="relative z-10">{index + 1}</span>
                </div>

                {/* Enhanced Icon */}
                <div className="absolute -top-3 -right-3 bg-white dark:bg-gray-800 rounded-full p-2 border-2 border-blue-500 dark:border-blue-400 shadow-lg group-hover:scale-110 group-hover:rotate-12 group-hover:shadow-blue-500/25 dark:group-hover:shadow-blue-400/25 transition-all duration-500">
                  {step.icon.startsWith("/") ? (
                    <img src={step.icon} alt={step.title} className="w-8 h-8" />
                  ) : (
                    <span className="text-3xl">{step.icon}</span>
                  )}
                </div>

                {/* Glow effect */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 scale-150" />
              </div>

              {/* Enhanced Content */}
              <div className="flex-1 pb-8 group-hover:translate-x-2 transition-transform duration-500">
                <h3 className="text-3xl font-bold mb-4 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
                  {step.title}
                </h3>
                <p className="text-muted-foreground text-lg leading-relaxed group-hover:text-foreground/80 transition-colors duration-300">
                  {step.description}
                </p>

                {/* Decorative line */}
                <div className="mt-6 h-1 w-0 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full group-hover:w-24 transition-all duration-700" />
              </div>

              {/* Floating particles */}
              <div className="absolute top-8 right-8 w-2 h-2 bg-blue-400 rounded-full opacity-0 group-hover:opacity-100 group-hover:animate-pulse transition-all duration-500" />
              <div
                className="absolute bottom-8 left-24 w-1 h-1 bg-purple-400 rounded-full opacity-0 group-hover:opacity-100 group-hover:animate-pulse transition-all duration-700"
                style={{ animationDelay: "0.3s" }}
              />
            </div>
          ))}
        </div>
      </div>
    </ContentSection>
  );
}
