import { Check } from "lucide-react";
import ContentSection from "./content-section";

interface BenefitItem {
  title: string;
  description: string;
  icon?: string;
}

interface BenefitsProps {
  title: string;
  description: string;
  items: BenefitItem[];
}

export default function Benefits({ title, description, items }: BenefitsProps) {
  return (
    <ContentSection
      title={title}
      description={description}
      background="gradient"
      decorations={true}
      padding="lg"
      headerSpacing="md"
      maxWidth="6xl"
    >
      {/* Benefits grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {items.map((benefit, index) => (
          <div
            key={`${benefit.title}-${index}`}
            className="group flex items-start space-x-4 p-6 rounded-xl bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm hover:bg-white/80 dark:hover:bg-gray-800/80 transition-all duration-300 hover:scale-105 hover:shadow-lg"
            style={{ animationDelay: `${index * 0.1}s` }}
          >
            {/* Icon */}
            <div className="flex-shrink-0">
              <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:shadow-green-500/25 transition-all duration-300">
                {benefit.icon ? (
                  benefit.icon.startsWith("/") ? (
                    <img src={benefit.icon} alt={benefit.title} className="w-6 h-6" />
                  ) : (
                    <span className="text-xl">{benefit.icon}</span>
                  )
                ) : (
                  <Check className="w-6 h-6 text-white" />
                )}
              </div>
            </div>

            {/* Content */}
            <div className="flex-1">
              <h3 className="text-xl font-semibold mb-2 group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors">
                {benefit.title}
              </h3>
              <p className="text-muted-foreground leading-relaxed">{benefit.description}</p>
            </div>
          </div>
        ))}
      </div>
    </ContentSection>
  );
}
