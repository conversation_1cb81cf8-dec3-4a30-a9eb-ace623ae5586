// app/lib/storage/image-storage.server.ts

import type {
  R2Bucket,
  R2ListOptions,
  R2Object,
  R2ObjectBody,
  R2Objects,
  R2PutOptions,
} from "@cloudflare/workers-types";

// Define a constant for the image storage path prefix
const IMAGE_PATH_PREFIX = "images/generated/";

export interface ImageMetadata {
  originalName?: string;
  contentType?: string;
  size?: number;
  uploaderId?: string; // Optional: if you track which user uploaded it
  [key: string]: string | number | undefined; // Allow other string/number properties
}

export class ImageStorageService {
  private r2Bucket: R2Bucket;
  private publicUrl?: string; // Optional: if you have a public R2 domain

  constructor(r2Bucket: R2Bucket, publicUrl?: string) {
    this.r2Bucket = r2Bucket;
    this.publicUrl = publicUrl?.replace(/\/$/, ""); // Remove trailing slash if any
  }

  /**
   * Generates a unique key for an image.
   * @param filename - The original filename of the image.
   * @returns A unique key for storing the image in R2.
   */
  private generateImageKey(filename: string): string {
    const timestamp = Date.now();
    const randomSuffix = Math.random().toString(36).substring(2, 8);
    // Ensure filename is a string and split correctly
    const nameParts = typeof filename === "string" ? filename.split(".") : [];
    const extension = nameParts.length > 1 ? nameParts.pop() : "png"; // Default to png if no extension or not a string
    return `${IMAGE_PATH_PREFIX}${timestamp}_${randomSuffix}.${extension}`;
  }

  /**
   * Uploads an image to R2.
   * @param imageFile - The image file (e.g., from a FormData).
   * @param metadata - Metadata associated with the image.
   * @returns The R2 object details or null if upload fails.
   */
  async uploadImage(
    imageFile: File,
    metadata: Partial<ImageMetadata> = {}
  ): Promise<R2Object | null> {
    const key = this.generateImageKey(imageFile.name);

    const httpMetadata: R2PutOptions["httpMetadata"] = {
      contentType: imageFile.type || metadata.contentType,
    };

    const customMetadata: R2PutOptions["customMetadata"] = {};
    if (metadata.originalName) customMetadata.originalName = metadata.originalName;
    if (metadata.uploaderId) customMetadata.uploaderId = metadata.uploaderId;
    // Add any other string-based metadata
    for (const metaKey in metadata) {
      if (
        Object.hasOwn(metadata, metaKey) &&
        typeof metadata[metaKey] === "string" &&
        !["originalName", "uploaderId", "contentType", "size"].includes(metaKey)
      ) {
        customMetadata[metaKey] = metadata[metaKey] as string;
      }
    }
    customMetadata.uploadedAt = new Date().toISOString();

    try {
      const uploadedObject = await this.r2Bucket.put(key, imageFile.stream(), {
        httpMetadata,
        customMetadata,
      });

      if (!uploadedObject) {
        console.error(`Failed to upload image to R2: ${key}`);
        return null;
      }
      // Return a complete R2Object like structure, R2Bucket.put returns R2Object on success
      return {
        ...uploadedObject, // Contains etag, version etc.
        key: key,
        size: imageFile.size, // put result doesn't have size, so use original
        httpMetadata: httpMetadata,
        customMetadata: customMetadata,
        uploaded: new Date(), // put result doesn't have uploaded date
      } as R2Object;
    } catch (error) {
      console.error(`Error uploading image ${key}:`, error);
      return null;
    }
  }

  /**
   * Retrieves an image object's body from R2.
   * @param key - The R2 object key.
   * @returns The R2 object body or null if not found.
   */
  async getImage(key: string): Promise<R2ObjectBody | null> {
    try {
      const object = await this.r2Bucket.get(key);
      if (!object || !object.body) {
        // R2GetResult does not have body directly, need to call .arrayBuffer() or .stream()
        return null;
      }
      return object; // R2ObjectBody is the R2GetResult itself
    } catch (error) {
      console.error(`Error retrieving image ${key}:`, error);
      return null;
    }
  }

  /**
   * Generates a public URL for an image if a public R2 domain is configured.
   * Generates a URL for an image.
   * Defaults to the controlled download endpoint for better security.
   * Can provide a direct public R2 URL if 'forcePublic' is true and publicUrl is configured.
   * Allows appending transformation parameters for public URLs (e.g., for Cloudflare Image Resizing).
   * @param key - The R2 object key.
   * @param forcePublic - If true and publicUrl is configured, returns a direct public R2 URL.
   * @param transformParams - Optional string of parameters to append to public URLs (e.g., "format=auto,width=800").
   * @returns The image URL.
   */
  getImageUrl(key: string, forcePublic = false, transformParams?: string): string {
    if (!key) return ""; // Handle null, undefined or empty key

    // If a public URL is configured and explicitly requested, provide it
    if (forcePublic && this.publicUrl) {
      // Ensure no double slashes if key might start with one
      const cleanKey = key.startsWith("/") ? key.substring(1) : key;
      let publicImageUrl = `${this.publicUrl}/${cleanKey}`;

      // Append transformation parameters if provided
      // For Cloudflare Image Resizing, params are typically appended after /cdn-cgi/image/
      // e.g., https://<zone>/cdn-cgi/image/format=auto,width=800/images/pic.jpg
      // This simple append might need adjustment based on exact CF resizing URL structure if not using path-based params.
      // A common way is to have the base URL already be the /cdn-cgi/image/ part.
      // If this.publicUrl = "https://example.com/cdn-cgi/image", then params are appended like "format=auto,width=300"
      // and the key is appended after.
      // For simplicity here, we'll assume this.publicUrl is the base path to the image,
      // and resizing params are query strings or part of a specific path structure
      // that the user of this service would know how to format transformParams.
      // A more robust solution would involve a structured object for transformations.

      // If transformParams are meant to be Cloudflare's path-based options:
      // e.g. this.publicUrl = "https://example.com"
      // key = "my-image.jpg"
      // transformParams = "width=800,format=auto"
      // result: "https://example.com/cdn-cgi/image/width=800,format=auto/my-image.jpg"
      // This requires this.publicUrl to NOT include /cdn-cgi/image/ and key to be the path after.

      // Simpler approach: assuming transformParams is a query string if not using CF-specific path.
      // Or, if using Cloudflare, the this.publicUrl might already be "https://your.domain/cdn-cgi/image/"
      // In that case, transformParams would be like "width=800,format=auto" and key is the path to the image.
      // Let's assume `this.publicUrl` is the base path to the R2 bucket (e.g. https://r2.mybucket.com)
      // and `transformParams` are query parameters for a service in front of it.
      // Or, if `this.publicUrl` itself is a Cloudflare Image Resizing endpoint like `https://example.com/cdn-cgi/image`
      // then `transformParams` could be `width=200,format=webp` and the key is the path to the image.
      // For this implementation, we'll assume `transformParams` are added as query string for generality,
      // unless a specific structure is known for `this.publicUrl`.

      // If Cloudflare Image Resizing is used with URL parameters on the /cdn-cgi/image/ path:
      // e.g. `https://<worker_url>/<options>/<image_path_in_r2>`
      // where this.publicUrl could be `https://<worker_url>`
      // transformParams could be `width=800,height=600,fit=cover`
      // and key is `images/generated/myimage.png`
      // The URL would be `https://<worker_url>/cdn-cgi/image/width=800,height=600,fit=cover/images/generated/myimage.png`
      // This structure is more common for CF Image Resizing.
      // Let's implement this specific structure if transformParams are present.

      if (transformParams && this.publicUrl.match(/https?:\/\//)) {
        // Basic check if publicUrl is a full URL
        // Assuming publicUrl does NOT end with /cdn-cgi/image/
        // And key is the full path to the image in the bucket.
        publicImageUrl = `${this.publicUrl}/cdn-cgi/image/${transformParams}/${cleanKey}`;
      }
      // If transformParams are NOT provided, it returns the direct public URL as before.

      return publicImageUrl;
    }

    // Default to the controlled download endpoint for all other cases
    // Transformation parameters are not typically applied to the secure download link directly,
    // as optimization would happen on the CDN/edge if using Cloudflare Resizing.
    // If optimization is done app-side, it's already optimized.
    return `/api/image-download?key=${encodeURIComponent(key)}`;
  }

  /**
   * Deletes an image from R2.
   * @param key - The R2 object key.
   * @returns True if deletion was successful, false otherwise.
   */
  async deleteImage(key: string): Promise<boolean> {
    try {
      await this.r2Bucket.delete(key);
      return true;
    } catch (error) {
      console.error(`Error deleting image ${key}:`, error);
      return false;
    }
  }

  /**
   * Lists images in the storage.
   * @param options - R2 list options (e.g., prefix, limit).
   * @returns A list of R2 objects.
   */
  async listImages(options?: R2ListOptions): Promise<R2Objects> {
    const listOptions: R2ListOptions = { ...options, prefix: options?.prefix || IMAGE_PATH_PREFIX };
    // Ensure limit is a number if provided
    if (listOptions.limit !== undefined && typeof listOptions.limit !== "number") {
      listOptions.limit = Number(listOptions.limit);
      if (isNaN(listOptions.limit)) {
        delete listOptions.limit; // Remove if not a valid number
      }
    }
    return this.r2Bucket.list(listOptions);
  }
}

// Example of how to initialize and use the service in a Remix loader/action:
/*
import type { ActionFunctionArgs } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";
import { ImageStorageService } from "~/lib/storage/image-storage.server"; // Adjust path as needed

export async function action({ request, context }: ActionFunctionArgs) {
  const R2_BUCKET = context.cloudflare.env.R2_BUCKET;
  if (!R2_BUCKET) {
    return json({ error: "R2 bucket not configured" }, { status: 503 });
  }

  const R2_PUBLIC_URL = context.cloudflare.env.R2_PUBLIC_URL as string | undefined;

  const imageService = new ImageStorageService(R2_BUCKET, R2_PUBLIC_URL);

  if (request.method === "POST") {
    try {
      const formData = await request.formData();
      const file = formData.get("image") as File;

      if (!file || !(file instanceof File)) {
        return json({ error: "No image file found or invalid file type" }, { status: 400 });
      }

      // Example of adding extra metadata
      const userId = "user-example-123"; // Replace with actual user ID from session or auth context

      const uploadedImage = await imageService.uploadImage(file, {
        originalName: file.name,
        uploaderId: userId,
        source: "user-upload", // Example custom metadata
      });

      if (!uploadedImage) {
        return json({ error: "Image upload failed" }, { status: 500 });
      }

      return json({
        success: true,
        image: {
          key: uploadedImage.key,
          url: imageService.getImageUrl(uploadedImage.key),
          size: uploadedImage.size,
          contentType: uploadedImage.httpMetadata?.contentType,
          etag: uploadedImage.etag,
          customMetadata: uploadedImage.customMetadata,
        },
      });
    } catch (error) {
      console.error("Upload action error:", error);
      return json(
        { error: "An unexpected error occurred during upload." },
        { status: 500 }
      );
    }
  }

  return json({ error: "Method not allowed" }, { status: 405 });
}
*/
