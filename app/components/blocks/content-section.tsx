interface ContentSectionProps {
  title?: string;
  description?: string;
  children: React.ReactNode;
  className?: string;
  background?:
    | "default"
    | "muted"
    | "gradient"
    | "showcase"
    | "testimonials"
    | "stats"
    | "features"
    | "cta";
  decorations?: boolean;
  padding?: "sm" | "md" | "lg" | "xl";
  headerSpacing?: "sm" | "md" | "lg";
  maxWidth?: "4xl" | "5xl" | "6xl" | "7xl";
}

export default function ContentSection({
  title,
  description,
  children,
  className = "",
  background = "default",
  decorations = true,
  padding = "lg",
  headerSpacing = "lg",
  maxWidth = "7xl",
}: ContentSectionProps) {
  const getBackgroundClass = () => {
    switch (background) {
      case "muted":
        return "bg-muted/30";
      case "gradient":
        return "bg-background";
      case "showcase":
        return "bg-muted/30";
      case "testimonials":
        return "bg-muted/30";
      case "stats":
        return "bg-background";
      case "features":
        return "bg-muted/30";
      case "cta":
        return "bg-muted/20";
      default:
        return "bg-background";
    }
  };

  const getPaddingClass = () => {
    switch (padding) {
      case "sm":
        return "py-12";
      case "md":
        return "py-16";
      case "xl":
        return "py-24";
      default:
        return "py-20";
    }
  };

  const getHeaderSpacingClass = () => {
    switch (headerSpacing) {
      case "sm":
        return "mb-8";
      case "md":
        return "mb-12";
      case "lg":
        return "mb-16";
      default:
        return "mb-12";
    }
  };

  const getMaxWidthClass = () => {
    switch (maxWidth) {
      case "4xl":
        return "max-w-4xl";
      case "5xl":
        return "max-w-5xl";
      case "6xl":
        return "max-w-6xl";
      default:
        return "max-w-7xl";
    }
  };

  const getDecorationsByBackground = () => {
    // Minimal decorations for clean ChatGPT-style design
    switch (background) {
      case "showcase":
      case "features":
      case "cta":
        return (
          <>
            {/* Subtle line decorations */}
            <div className="absolute top-1/4 right-0 w-px h-24 bg-border opacity-50" />
            <div className="absolute bottom-1/4 left-0 w-px h-24 bg-border opacity-50" />
          </>
        );
      default:
        return null;
    }
  };

  return (
    <section
      className={`${getPaddingClass()} ${getBackgroundClass()} relative overflow-hidden ${className}`}
    >
      {/* Background decorations */}
      {decorations && <div className="absolute inset-0">{getDecorationsByBackground()}</div>}

      <div className={`${getMaxWidthClass()} mx-auto px-4 relative z-10`}>
        {/* Header */}
        {(title || description) && (
          <div className={`text-center ${getHeaderSpacingClass()}`}>
            {title && (
              <h2 className="text-3xl font-bold lg:text-4xl text-foreground leading-tight mb-6">
                {title}
              </h2>
            )}
            {description && (
              <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                {description}
              </p>
            )}
          </div>
        )}

        {/* Content */}
        {children}
      </div>
    </section>
  );
}
