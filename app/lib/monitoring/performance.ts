/**
 * Enhanced Performance Monitoring System
 * Comprehensive performance tracking for client and server-side metrics
 */

import { event } from "./analytics";

export interface PerformanceMetrics {
  navigationTiming?: PerformanceNavigationTiming;
  resourceTiming?: PerformanceResourceTiming[];
  webVitals?: {
    lcp?: number;
    fid?: number;
    cls?: number;
    fcp?: number;
    ttfb?: number;
  };
  serverMetrics?: ServerMetrics;
  customMetrics?: Record<string, number>;
}

export interface ServerMetrics {
  responseTime: number;
  memoryUsage: {
    used: number;
    total: number;
    percentage: number;
  };
  cpuUsage?: number;
  activeConnections: number;
  cacheHitRate: number;
  databaseResponseTime: number;
  aiResponseTime?: number;
}

/**
 * Performance monitoring configuration
 */
export const performanceConfig = {
  enabled:
    typeof window !== "undefined" &&
    !window.location.hostname.includes("localhost") &&
    !window.location.hostname.includes("127.0.0.1"),
  debug:
    typeof window !== "undefined" &&
    (window.location.hostname.includes("localhost") ||
      window.location.hostname.includes("127.0.0.1")),
  thresholds: {
    lcp: 2500, // Good LCP is under 2.5s
    fid: 100, // Good FID is under 100ms
    cls: 0.1, // Good CLS is under 0.1
    fcp: 1800, // Good FCP is under 1.8s
    ttfb: 800, // Good TTFB is under 800ms
  },
};

/**
 * Initialize performance monitoring
 */
export function initPerformanceMonitoring() {
  if (!performanceConfig.enabled && !performanceConfig.debug) return;

  // Monitor navigation timing
  monitorNavigationTiming();

  // Monitor resource loading
  monitorResourceTiming();

  // Monitor Core Web Vitals
  monitorWebVitals();

  // Monitor long tasks
  monitorLongTasks();

  if (performanceConfig.debug) {
    console.log("Performance monitoring initialized");
  }
}

/**
 * Monitor navigation timing
 */
function monitorNavigationTiming() {
  if (typeof window === "undefined" || !window.performance) return;

  window.addEventListener("load", () => {
    setTimeout(() => {
      const navigation = performance.getEntriesByType(
        "navigation"
      )[0] as PerformanceNavigationTiming;

      if (navigation) {
        const metrics = {
          dns: navigation.domainLookupEnd - navigation.domainLookupStart,
          tcp: navigation.connectEnd - navigation.connectStart,
          ttfb: navigation.responseStart - navigation.requestStart,
          download: navigation.responseEnd - navigation.responseStart,
          domParse: navigation.domContentLoadedEventEnd - navigation.responseEnd,
          domReady: navigation.domContentLoadedEventEnd - navigation.navigationStart,
          loadComplete: navigation.loadEventEnd - navigation.navigationStart,
        };

        // Track key metrics
        event({
          action: "navigation_timing",
          category: "Performance",
          label: "TTFB",
          value: Math.round(metrics.ttfb),
        });

        event({
          action: "navigation_timing",
          category: "Performance",
          label: "DOM_Ready",
          value: Math.round(metrics.domReady),
        });

        event({
          action: "navigation_timing",
          category: "Performance",
          label: "Load_Complete",
          value: Math.round(metrics.loadComplete),
        });

        if (performanceConfig.debug) {
          console.log("Navigation timing:", metrics);
        }
      }
    }, 0);
  });
}

/**
 * Monitor resource loading performance
 */
function monitorResourceTiming() {
  if (typeof window === "undefined" || !window.performance) return;

  window.addEventListener("load", () => {
    setTimeout(() => {
      const resources = performance.getEntriesByType("resource") as PerformanceResourceTiming[];

      // Group resources by type
      const resourcesByType = resources.reduce(
        (acc, resource) => {
          const type = getResourceType(resource.name);
          if (!acc[type]) acc[type] = [];
          acc[type].push(resource);
          return acc;
        },
        {} as Record<string, PerformanceResourceTiming[]>
      );

      // Track slow resources
      Object.entries(resourcesByType).forEach(([type, typeResources]) => {
        const slowResources = typeResources.filter((r) => r.duration > 1000);

        if (slowResources.length > 0) {
          event({
            action: "slow_resource",
            category: "Performance",
            label: type,
            value: slowResources.length,
            custom_parameters: {
              avg_duration: Math.round(
                slowResources.reduce((sum, r) => sum + r.duration, 0) / slowResources.length
              ),
            },
          });
        }
      });

      if (performanceConfig.debug) {
        console.log("Resource timing:", resourcesByType);
      }
    }, 1000);
  });
}

/**
 * Get resource type from URL
 */
function getResourceType(url: string): string {
  if (url.includes(".js")) return "script";
  if (url.includes(".css")) return "stylesheet";
  if (url.match(/\.(jpg|jpeg|png|gif|webp|svg)$/)) return "image";
  if (url.includes("font")) return "font";
  return "other";
}

/**
 * Monitor Core Web Vitals with enhanced tracking
 */
function monitorWebVitals() {
  if (typeof window === "undefined" || !("PerformanceObserver" in window)) return;

  // Largest Contentful Paint (LCP)
  try {
    const lcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];

      const lcp = lastEntry.startTime;
      const isGood = lcp <= performanceConfig.thresholds.lcp;

      event({
        action: "web_vital",
        category: "Performance",
        label: "LCP",
        value: Math.round(lcp),
        custom_parameters: {
          rating: isGood ? "good" : lcp <= 4000 ? "needs_improvement" : "poor",
        },
      });

      if (performanceConfig.debug) {
        console.log("LCP:", lcp, isGood ? "✅" : "❌");
      }
    });
    lcpObserver.observe({ entryTypes: ["largest-contentful-paint"] });
  } catch (error) {
    if (performanceConfig.debug) {
      console.warn("Failed to observe LCP:", error);
    }
  }

  // First Contentful Paint (FCP)
  try {
    const fcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const fcpEntry = entries.find((entry) => entry.name === "first-contentful-paint");

      if (fcpEntry) {
        const fcp = fcpEntry.startTime;
        const isGood = fcp <= performanceConfig.thresholds.fcp;

        event({
          action: "web_vital",
          category: "Performance",
          label: "FCP",
          value: Math.round(fcp),
          custom_parameters: {
            rating: isGood ? "good" : fcp <= 3000 ? "needs_improvement" : "poor",
          },
        });

        if (performanceConfig.debug) {
          console.log("FCP:", fcp, isGood ? "✅" : "❌");
        }
      }
    });
    fcpObserver.observe({ entryTypes: ["paint"] });
  } catch (error) {
    if (performanceConfig.debug) {
      console.warn("Failed to observe FCP:", error);
    }
  }

  // First Input Delay (FID)
  try {
    const fidObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry) => {
        const fid = (entry as any).processingStart - entry.startTime;
        const isGood = fid <= performanceConfig.thresholds.fid;

        event({
          action: "web_vital",
          category: "Performance",
          label: "FID",
          value: Math.round(fid),
          custom_parameters: {
            rating: isGood ? "good" : fid <= 300 ? "needs_improvement" : "poor",
          },
        });

        if (performanceConfig.debug) {
          console.log("FID:", fid, isGood ? "✅" : "❌");
        }
      });
    });
    fidObserver.observe({ entryTypes: ["first-input"] });
  } catch (error) {
    if (performanceConfig.debug) {
      console.warn("Failed to observe FID:", error);
    }
  }

  // Cumulative Layout Shift (CLS)
  try {
    let clsValue = 0;
    const clsObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry) => {
        if (!(entry as any).hadRecentInput) {
          clsValue += (entry as any).value;
        }
      });
    });
    clsObserver.observe({ entryTypes: ["layout-shift"] });

    // Report CLS when page is unloaded
    window.addEventListener("beforeunload", () => {
      const isGood = clsValue <= performanceConfig.thresholds.cls;

      event({
        action: "web_vital",
        category: "Performance",
        label: "CLS",
        value: Math.round(clsValue * 1000), // Convert to milliseconds for GA
        custom_parameters: {
          rating: isGood ? "good" : clsValue <= 0.25 ? "needs_improvement" : "poor",
        },
      });

      if (performanceConfig.debug) {
        console.log("CLS:", clsValue, isGood ? "✅" : "❌");
      }
    });
  } catch (error) {
    if (performanceConfig.debug) {
      console.warn("Failed to observe CLS:", error);
    }
  }
}

/**
 * Monitor long tasks that block the main thread
 */
function monitorLongTasks() {
  if (typeof window === "undefined" || !("PerformanceObserver" in window)) return;

  try {
    const longTaskObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry) => {
        event({
          action: "long_task",
          category: "Performance",
          label: "Main_Thread_Blocked",
          value: Math.round(entry.duration),
          custom_parameters: {
            start_time: Math.round(entry.startTime),
          },
        });

        if (performanceConfig.debug) {
          console.warn("Long task detected:", entry.duration + "ms");
        }
      });
    });
    longTaskObserver.observe({ entryTypes: ["longtask"] });
  } catch (error) {
    if (performanceConfig.debug) {
      console.warn("Failed to observe long tasks:", error);
    }
  }
}

// Performance metrics storage
const performanceMetrics: {
  requests: Array<{
    timestamp: number;
    responseTime: number;
    endpoint: string;
    method: string;
    status: number;
  }>;
  serverMetrics: Array<ServerMetrics & { timestamp: number }>;
  webVitals: Array<{
    timestamp: number;
    lcp?: number;
    fid?: number;
    cls?: number;
    fcp?: number;
    ttfb?: number;
  }>;
} = {
  requests: [],
  serverMetrics: [],
  webVitals: [],
};

/**
 * Track server-side performance metrics
 */
export function trackServerPerformance(
  endpoint: string,
  method: string,
  responseTime: number,
  status: number,
  additionalMetrics?: Partial<ServerMetrics>
): void {
  const metrics: ServerMetrics = {
    responseTime,
    memoryUsage: getMemoryUsage(),
    activeConnections: 0, // Would be tracked by connection pool
    cacheHitRate: 0, // Would be provided by cache manager
    databaseResponseTime: 0, // Would be provided by database optimizer
    ...additionalMetrics,
  };

  // Store request metrics
  performanceMetrics.requests.push({
    timestamp: Date.now(),
    responseTime,
    endpoint,
    method,
    status,
  });

  // Store server metrics
  performanceMetrics.serverMetrics.push({
    ...metrics,
    timestamp: Date.now(),
  });

  // Keep only recent metrics (last 1000 entries)
  if (performanceMetrics.requests.length > 1000) {
    performanceMetrics.requests = performanceMetrics.requests.slice(-1000);
  }
  if (performanceMetrics.serverMetrics.length > 1000) {
    performanceMetrics.serverMetrics = performanceMetrics.serverMetrics.slice(-1000);
  }

  // Track with analytics
  event({
    action: "server_performance",
    category: "Performance",
    label: endpoint,
    value: Math.round(responseTime),
    custom_parameters: {
      method,
      status,
      memoryUsage: metrics.memoryUsage.percentage,
    },
  });
}

/**
 * Get current memory usage
 */
function getMemoryUsage(): { used: number; total: number; percentage: number } {
  if (typeof process !== "undefined" && process.memoryUsage) {
    const usage = process.memoryUsage();
    const used = usage.heapUsed;
    const total = usage.heapTotal;
    return {
      used,
      total,
      percentage: (used / total) * 100,
    };
  }

  return { used: 0, total: 0, percentage: 0 };
}

/**
 * Track performance metrics
 */
export function trackPerformance(metrics: PerformanceMetrics) {
  // Store web vitals if provided
  if (metrics.webVitals) {
    performanceMetrics.webVitals.push({
      timestamp: Date.now(),
      ...metrics.webVitals,
    });

    // Keep only recent web vitals (last 100 entries)
    if (performanceMetrics.webVitals.length > 100) {
      performanceMetrics.webVitals = performanceMetrics.webVitals.slice(-100);
    }
  }

  event({
    action: "performance",
    category: "Performance",
    label: "Custom_Metrics",
    custom_parameters: {
      ...metrics,
      timestamp: Date.now(),
    },
  });
}

/**
 * Get current performance metrics
 */
export function getPerformanceMetrics(): PerformanceMetrics {
  if (typeof window === "undefined" || !window.performance) {
    return {};
  }

  const navigation = performance.getEntriesByType("navigation")[0] as PerformanceNavigationTiming;
  const resources = performance.getEntriesByType("resource") as PerformanceResourceTiming[];

  return {
    navigationTiming: navigation,
    resourceTiming: resources,
  };
}

/**
 * Get server performance statistics
 */
export function getServerPerformanceStats(): {
  avgResponseTime: number;
  requestCount: number;
  errorRate: number;
  memoryUsage: { used: number; total: number; percentage: number };
  recentRequests: typeof performanceMetrics.requests;
} {
  const recentRequests = performanceMetrics.requests.slice(-100);
  const avgResponseTime =
    recentRequests.length > 0
      ? recentRequests.reduce((sum, req) => sum + req.responseTime, 0) / recentRequests.length
      : 0;

  const errorCount = recentRequests.filter((req) => req.status >= 400).length;
  const errorRate = recentRequests.length > 0 ? (errorCount / recentRequests.length) * 100 : 0;

  return {
    avgResponseTime: Math.round(avgResponseTime),
    requestCount: performanceMetrics.requests.length,
    errorRate: Math.round(errorRate * 100) / 100,
    memoryUsage: getMemoryUsage(),
    recentRequests,
  };
}
