import { Link } from "@remix-run/react";
import { ArrowRight, Play } from "lucide-react";
import { Button } from "~/components/ui/button";
import ContentSection from "./content-section";

interface IntroduceProps {
  title: string;
  description: string;
  content: string;
  image?: string;
  video?: string;
  buttons?: Array<{
    title: string;
    url: string;
    variant?: "default" | "outline" | "secondary" | "ghost" | "link" | "destructive";
  }>;
  features?: string[];
  reversed?: boolean;
}

export default function Introduce({
  title,
  description,
  content,
  image,
  video,
  buttons,
  features,
  reversed = false,
}: IntroduceProps) {
  return (
    <ContentSection
      title={title}
      description={description}
      background="default"
      decorations={true}
      padding="lg"
      headerSpacing="lg"
    >
      <div
        className={`grid grid-cols-1 lg:grid-cols-2 gap-12 items-center ${reversed ? "lg:grid-flow-col-dense" : ""}`}
      >
        {/* Content */}
        <div className={`space-y-6 ${reversed ? "lg:col-start-2" : ""}`}>
          <div className="prose prose-lg dark:prose-invert max-w-none">
            <p className="text-lg text-muted-foreground leading-relaxed">{content}</p>
          </div>

          {/* Features list */}
          {features && features.length > 0 && (
            <ul className="space-y-3">
              {features.map((feature, index) => (
                <li
                  key={`${feature.slice(0, 20)}-${index}`}
                  className="flex items-center space-x-3"
                >
                  <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full" />
                  <span className="text-muted-foreground">{feature}</span>
                </li>
              ))}
            </ul>
          )}

          {/* Action buttons */}
          {buttons && buttons.length > 0 && (
            <div className="flex flex-col sm:flex-row gap-4 pt-4">
              {buttons.map((button, index) => (
                <Button
                  key={`${button.text || button.label}-${index}`}
                  asChild
                  variant={button.variant || "default"}
                  size="lg"
                  className="group hover:scale-105 transition-all duration-300"
                >
                  <Link to={button.url} className="flex items-center gap-2">
                    {button.title}
                    <ArrowRight className="w-4 h-4 transition-transform group-hover:translate-x-1" />
                  </Link>
                </Button>
              ))}
            </div>
          )}
        </div>

        {/* Visual */}
        <div className={`relative ${reversed ? "lg:col-start-1" : ""}`}>
          <div className="relative aspect-video rounded-2xl overflow-hidden bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-900 shadow-2xl">
            {video ? (
              <div className="relative w-full h-full group cursor-pointer">
                {image && <img src={image} alt={title} className="w-full h-full object-cover" />}
                <div className="absolute inset-0 bg-black/30 flex items-center justify-center group-hover:bg-black/40 transition-colors">
                  <div className="w-20 h-20 bg-white/90 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform">
                    <Play className="w-8 h-8 text-gray-900 ml-1" />
                  </div>
                </div>
              </div>
            ) : image ? (
              <img src={image} alt={title} className="w-full h-full object-cover" />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl text-white">🚀</span>
                  </div>
                  <p className="text-muted-foreground">Visual content placeholder</p>
                </div>
              </div>
            )}
          </div>

          {/* Decorative elements */}
          <div className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-full blur-xl" />
          <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-gradient-to-tr from-purple-500/20 to-pink-500/20 rounded-full blur-xl" />
        </div>
      </div>
    </ContentSection>
  );
}
