// A simple placeholder replacer for now.
// For more complex scenarios, consider libraries like handlebars or nunjucks,
// or leverage @react-email/render more directly if using React components for emails.

interface TemplateVariables {
  [key: string]: string | number | boolean | undefined;
}

export const populateTemplate = (content: string, variables: TemplateVariables): string => {
  let populatedContent = content;
  for (const key in variables) {
    if (Object.hasOwn(variables, key)) {
      const placeholder = `{{${key}}}`; // e.g., {{name}}
      populatedContent = populatedContent.replace(
        new RegExp(placeholder, "g"),
        String(variables[key])
      );
    }
  }
  return populatedContent;
};

// Interface for a generic email template structure
export interface EmailTemplate<T extends TemplateVariables = TemplateVariables> {
  subject: (vars: T) => string;
  html: (vars: T) => string;
  text: (vars: T) => string;
}
