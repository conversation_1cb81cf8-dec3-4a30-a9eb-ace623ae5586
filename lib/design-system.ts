/**
 * Design System Configuration
 * Centralized design tokens and layout utilities
 */

export const designSystem = {
  layouts: {
    // Standard container with max width and padding
    container: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",
    
    // Smaller container for focused content
    containerSmall: "max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",
    
    // Extra small container for forms and narrow content
    containerXSmall: "max-w-2xl mx-auto px-4 sm:px-6 lg:px-8",
    
    // Full width container
    containerFull: "w-full px-4 sm:px-6 lg:px-8",
  },
  
  spacing: {
    section: "py-16 lg:py-24",
    sectionSmall: "py-8 lg:py-12",
    sectionLarge: "py-24 lg:py-32",
  },
  
  typography: {
    h1: "text-4xl md:text-5xl lg:text-6xl font-bold",
    h2: "text-3xl md:text-4xl lg:text-5xl font-bold",
    h3: "text-2xl md:text-3xl lg:text-4xl font-bold",
    h4: "text-xl md:text-2xl lg:text-3xl font-semibold",
    h5: "text-lg md:text-xl lg:text-2xl font-semibold",
    h6: "text-base md:text-lg lg:text-xl font-semibold",
    body: "text-base leading-relaxed",
    bodyLarge: "text-lg leading-relaxed",
    bodySmall: "text-sm leading-relaxed",
    caption: "text-xs text-muted-foreground",
  },
  
  colors: {
    gradients: {
      primary: "bg-gradient-to-r from-blue-600 via-purple-600 to-cyan-600",
      primaryHover: "hover:from-blue-700 hover:via-purple-700 hover:to-cyan-700",
      text: "bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 dark:from-white dark:via-blue-200 dark:to-purple-200 bg-clip-text text-transparent",
      background: "bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-blue-950/20 dark:via-background dark:to-purple-950/20",
    },
    patterns: {
      dots: "bg-[radial-gradient(circle_at_1px_1px,_hsl(var(--muted))_1px,_transparent_0)] bg-[size:20px_20px]",
      grid: "bg-[linear-gradient(to_right,_hsl(var(--border))_1px,_transparent_1px),_linear-gradient(to_bottom,_hsl(var(--border))_1px,_transparent_1px)] bg-[size:20px_20px]",
    },
  },
  
  animations: {
    fadeIn: "animate-fade-in",
    slideUp: "animate-slide-up",
    pulse: "animate-pulse",
    bounce: "animate-bounce",
    spin: "animate-spin",
  },
  
  shadows: {
    card: "shadow-lg hover:shadow-xl transition-shadow duration-300",
    button: "shadow-xl hover:shadow-2xl",
    glow: "shadow-2xl hover:shadow-blue-500/40",
  },
  
  borders: {
    default: "border border-gray-200 dark:border-gray-700",
    rounded: "rounded-lg",
    roundedFull: "rounded-full",
    roundedXl: "rounded-xl",
    rounded2xl: "rounded-2xl",
  },
  
  backdrop: {
    blur: "backdrop-blur-sm",
    blurMd: "backdrop-blur-md",
    blurLg: "backdrop-blur-lg",
  },
} as const;

export type DesignSystem = typeof designSystem;
