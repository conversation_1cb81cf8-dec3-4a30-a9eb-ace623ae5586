/**
 * Usage History Page
 * Shows detailed API usage analytics and history
 */

import type { LoaderFunctionArgs } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";
import { useLoaderData } from "@remix-run/react";
import {
  Activity,
  AlertCircle,
  BarChart3,
  Calendar,
  CheckCircle,
  Clock,
  Download,
  RefreshCw,
  TrendingUp,
  XCircle,
  Zap,
} from "lucide-react";
import { useState } from "react";
import UsageAnalytics from "~/components/dashboard/usage-analytics";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { createDbFromEnv } from "~/lib/db";
import { findUserByUuid } from "~/models/user";
import { getRecentUsage, getUserAnalytics } from "~/services/analytics.server";
import { getUserUuid } from "~/services/user-management.server";

export async function loader({ request, context }: LoaderFunctionArgs) {
  try {
    const db = context.db || createDbFromEnv(context.cloudflare?.env);

    const userUuid = await getUserUuid();
    if (!userUuid) {
      return json({ success: false, error: "Not authenticated" }, { status: 401 });
    }

    const user = await findUserByUuid(userUuid, db);
    if (!user) {
      return json({ success: false, error: "User not found" }, { status: 404 });
    }

    // Get usage analytics for different periods
    const endDate = new Date();
    const last7Days = new Date(endDate.getTime() - 7 * 24 * 60 * 60 * 1000);
    const last30Days = new Date(endDate.getTime() - 30 * 24 * 60 * 60 * 1000);

    const [weeklyAnalytics, monthlyAnalytics, recentUsage] = await Promise.all([
      getUserAnalytics(userUuid, { startDate: last7Days, endDate }, db),
      getUserAnalytics(userUuid, { startDate: last30Days, endDate }, db),
      getRecentUsage(userUuid, 50, db),
    ]);

    return json({
      success: true,
      data: {
        user: {
          uuid: user.uuid,
          name: user.name,
          email: user.email,
          credits: user.credits,
        },
        analytics: {
          weekly: weeklyAnalytics,
          monthly: monthlyAnalytics,
        },
        recentUsage,
      },
    });
  } catch (error) {
    console.error("Error loading usage page:", error);
    return json({ success: false, error: "Failed to load usage data" }, { status: 500 });
  }
}

export default function UsagePage() {
  const { data } = useLoaderData<typeof loader>();
  const [selectedPeriod, setSelectedPeriod] = useState("weekly");

  const currentAnalytics =
    selectedPeriod === "weekly" ? data.analytics.weekly : data.analytics.monthly;

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "success":
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case "error":
        return <XCircle className="w-4 h-4 text-red-500" />;
      case "timeout":
        return <AlertCircle className="w-4 h-4 text-yellow-500" />;
      default:
        return <Activity className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "success":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
      case "error":
        return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";
      case "timeout":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
    }
  };

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
  };

  const successRate =
    currentAnalytics.totalRequests > 0
      ? ((currentAnalytics.successfulRequests / currentAnalytics.totalRequests) * 100).toFixed(1)
      : "0";

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Usage Analytics</h1>
              <p className="mt-2 text-gray-600 dark:text-gray-400">
                Monitor your API usage and performance metrics
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
                Export Data
              </Button>
              <Button variant="outline" size="sm">
                <RefreshCw className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Period Selector */}
        <div className="mb-6">
          <Tabs value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <TabsList>
              <TabsTrigger value="weekly">Last 7 Days</TabsTrigger>
              <TabsTrigger value="monthly">Last 30 Days</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        {/* Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Requests</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatNumber(currentAnalytics.totalRequests)}
              </div>
              <p className="text-xs text-muted-foreground">{successRate}% success rate</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Tokens Used</CardTitle>
              <Zap className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatNumber(currentAnalytics.totalTokens)}</div>
              <p className="text-xs text-muted-foreground">
                Avg:{" "}
                {currentAnalytics.totalRequests > 0
                  ? Math.round(currentAnalytics.totalTokens / currentAnalytics.totalRequests)
                  : 0}{" "}
                per request
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Credits Used</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatNumber(currentAnalytics.totalCredits)}
              </div>
              <p className="text-xs text-muted-foreground">
                ${currentAnalytics.totalCost.toFixed(4)} estimated cost
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Response Time</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatDuration(currentAnalytics.avgResponseTime)}
              </div>
              <p className="text-xs text-muted-foreground">
                {currentAnalytics.topProvider && `Top: ${currentAnalytics.topProvider}`}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Usage Breakdown */}
          <div className="lg:col-span-1 space-y-6">
            {/* Top Providers */}
            <Card>
              <CardHeader>
                <CardTitle>Top AI Providers</CardTitle>
                <CardDescription>
                  Most used providers in{" "}
                  {selectedPeriod === "weekly" ? "last 7 days" : "last 30 days"}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Object.entries(currentAnalytics.requestsByProvider)
                    .sort(([, a], [, b]) => b - a)
                    .slice(0, 5)
                    .map(([provider, count]) => (
                      <div key={provider} className="flex items-center justify-between">
                        <span className="capitalize text-sm">{provider}</span>
                        <Badge variant="secondary">{formatNumber(count)}</Badge>
                      </div>
                    ))}
                  {Object.keys(currentAnalytics.requestsByProvider).length === 0 && (
                    <div className="text-center text-gray-500 py-4">No usage data available</div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Top Models */}
            <Card>
              <CardHeader>
                <CardTitle>Top AI Models</CardTitle>
                <CardDescription>Most used models</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Object.entries(currentAnalytics.requestsByModel)
                    .sort(([, a], [, b]) => b - a)
                    .slice(0, 5)
                    .map(([model, count]) => (
                      <div key={model} className="flex items-center justify-between">
                        <span className="text-sm truncate">{model}</span>
                        <Badge variant="secondary">{formatNumber(count)}</Badge>
                      </div>
                    ))}
                  {Object.keys(currentAnalytics.requestsByModel).length === 0 && (
                    <div className="text-center text-gray-500 py-4">No usage data available</div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Status Breakdown */}
            <Card>
              <CardHeader>
                <CardTitle>Request Status</CardTitle>
                <CardDescription>Success and error rates</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Object.entries(currentAnalytics.requestsByStatus).map(([status, count]) => (
                    <div key={status} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(status)}
                        <span className="capitalize text-sm">{status}</span>
                      </div>
                      <Badge variant="secondary">{formatNumber(count)}</Badge>
                    </div>
                  ))}
                  {Object.keys(currentAnalytics.requestsByStatus).length === 0 && (
                    <div className="text-center text-gray-500 py-4">No usage data available</div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Detailed Analytics and Recent Usage */}
          <div className="lg:col-span-2">
            <Tabs defaultValue="analytics" className="space-y-4">
              <TabsList>
                <TabsTrigger value="analytics">Analytics Dashboard</TabsTrigger>
                <TabsTrigger value="recent">Recent Activity</TabsTrigger>
              </TabsList>

              <TabsContent value="analytics">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <BarChart3 className="w-5 h-5" />
                      <span>Detailed Analytics</span>
                    </CardTitle>
                    <CardDescription>Comprehensive usage analytics and trends</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <UsageAnalytics />
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="recent">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Calendar className="w-5 h-5" />
                      <span>Recent API Calls</span>
                    </CardTitle>
                    <CardDescription>
                      Last 50 API requests with detailed information
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {data.recentUsage.length === 0 ? (
                        <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                          <Activity className="w-12 h-12 mx-auto mb-4 opacity-50" />
                          <p>No recent usage found</p>
                          <p className="text-sm">Your API calls will appear here</p>
                        </div>
                      ) : (
                        data.recentUsage.map((usage) => (
                          <div
                            key={usage.id}
                            className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                          >
                            <div className="flex items-center space-x-3">
                              {getStatusIcon(usage.status)}
                              <div>
                                <div className="font-medium text-sm">{usage.endpoint}</div>
                                <div className="text-xs text-gray-500">
                                  {usage.provider && `${usage.provider} • `}
                                  {usage.model && `${usage.model} • `}
                                  {formatDuration(usage.duration)}
                                  {usage.tokensUsed && ` • ${usage.tokensUsed} tokens`}
                                  {usage.creditsUsed && ` • ${usage.creditsUsed} credits`}
                                </div>
                              </div>
                            </div>
                            <div className="text-right">
                              <Badge className={getStatusColor(usage.status)}>{usage.status}</Badge>
                              <div className="text-xs text-gray-500 mt-1">
                                {new Date(usage.createdAt).toLocaleTimeString()}
                              </div>
                            </div>
                          </div>
                        ))
                      )}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  );
}
