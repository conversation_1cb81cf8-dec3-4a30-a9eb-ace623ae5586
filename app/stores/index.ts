// Export all store types

// Export app store
export {
  selectIsInitialized,
  selectIsOnline,
  useAppActions,
  useAppStore,
  useIsInitialized,
  useIsOnline,
} from "./appStore";
// Export auth store (unified user management)
export {
  type User,
  useAuthActions as useUserActions,
  useAuthError as useUserError,
  useAuthLoading as useUserLoading,
  useAuthStore,
  useAuthStore as useUserStore, // Backward compatibility alias
  useIsAuthenticated,
  useUser,
} from "./auth-store";
// Export cart store
export {
  selectCartIsOpen,
  selectCartItemCount,
  selectCartItems,
  selectCartTotal,
  useCartActions,
  useCartIsOpen,
  useCartItemCount,
  useCartItems,
  useCartStore,
  useCartTotal,
} from "./cartStore";
export type * from "./types";
// Export UI store
export {
  selectNotifications,
  selectSidebarOpen,
  useNotifications,
  useSidebarOpen,
  useUIActions,
  useUIStore,
} from "./uiStore";
