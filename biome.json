{"$schema": "https://biomejs.dev/schemas/2.0.2/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false, "includes": ["**/*.js", "**/*.jsx", "**/*.ts", "**/*.tsx", "**/*.json", "!**/build/**", "!**/public/build/**", "!**/node_modules/**", "!**/.cache/**", "!**/drizzle/**", "!**/*.lock", "!**/wrangler.toml"]}, "formatter": {"enabled": true, "lineWidth": 100, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf", "formatWithErrors": false}, "javascript": {"formatter": {"quoteStyle": "double", "trailingCommas": "es5", "semicolons": "always", "arrowParentheses": "always"}}, "json": {"formatter": {"trailingCommas": "none"}}, "assist": {"actions": {"source": {"organizeImports": "on"}}}, "linter": {"enabled": true, "rules": {"recommended": true, "a11y": {"recommended": true, "noRedundantAlt": "off", "noSvgWithoutTitle": "off"}, "complexity": {"recommended": true, "noForEach": "off", "useLiteralKeys": "off", "useArrowFunction": "off"}, "correctness": {"recommended": true, "useExhaustiveDependencies": "off"}, "style": {"recommended": true, "useImportType": "error", "noNonNullAssertion": "off", "useSelfClosingElements": "error", "useConst": "error", "useTemplate": "off", "noUselessElse": "off", "useNodejsImportProtocol": "off", "useNumberNamespace": "off", "noParameterAssign": "error", "useAsConstAssertion": "error", "useDefaultParameterLast": "error", "useEnumInitializers": "error", "useSingleVarDeclarator": "error", "noUnusedTemplateLiteral": "error", "noInferrableTypes": "error"}, "suspicious": {"recommended": true, "noExplicitAny": "warn", "noImplicitAnyLet": "off", "noVar": "error"}, "security": {"recommended": true, "noDangerouslySetInnerHtml": "off"}}}}