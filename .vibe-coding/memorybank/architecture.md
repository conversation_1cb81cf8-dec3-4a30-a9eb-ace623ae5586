# Gin AI 后端架构设计

## 1. 系统整体架构

### 1.1 架构概述

本项目采用分层架构设计，遵循关注点分离原则，主要分为以下几层：

- **表示层**：处理 HTTP 请求和响应，由 Gin 框架实现。
- **业务逻辑层**：包含核心业务逻辑，处理用户认证、AI 调用、文件管理等功能。
- **数据访问层**：负责与数据库和外部服务的交互。
- **基础设施层**：提供配置管理、日志记录、错误处理等通用功能。

### 1.2 系统架构图

```
+-----------------------+
|      客户端应用        |
+-----------------------+
           |
           | HTTP/HTTPS
           v
+-----------------------+
|     API 网关/负载均衡   |
+-----------------------+
           |
           v
+---------------------------------------+
|            Gin Web 框架               |
|  +-------------+-------------------+  |
|  | 中间件       | 路由器             |  |
|  | (认证,日志等) |                   |  |
|  +-------------+-------------------+  |
+---------------------------------------+
           |
           v
+---------------------------------------+
|            业务逻辑层                  |
|  +-------------+-------------------+  |
|  | 用户服务     | AI 服务            |  |
|  +-------------+-------------------+  |
|  | Token管理   | 文件存储服务        |  |
|  +-------------+-------------------+  |
+---------------------------------------+
           |
           v
+---------------------------------------+
|            数据访问层                  |
|  +-------------+-------------------+  |
|  | 数据库管理器  | 外部API客户端      |  |
|  +-------------+-------------------+  |
+---------------------------------------+
           |
           v
+---------------+      +------------------+
|  PostgreSQL   |      | Cloudflare R2    |
|  数据库        |      | 对象存储         |
+---------------+      +------------------+
        |                     |
        v                     v
+---------------+      +------------------+
|  OpenAI API   |      | 其他外部服务      |
|  或其他AI服务  |      |                  |
+---------------+      +------------------+
```

## 2. 核心组件设计

### 2.1 用户认证与授权组件

#### 设计
- 基于 JWT (JSON Web Token) 的认证系统
- 支持用户注册、登录和 Token 刷新
- 角色基础的访问控制 (RBAC)

#### 关键接口
- `/api/v1/auth/register` - 用户注册
- `/api/v1/auth/login` - 用户登录
- `/api/v1/auth/refresh` - 刷新访问令牌
- `/api/v1/auth/logout` - 用户登出

#### 安全考虑
- 密码使用 bcrypt 哈希算法存储
- Token 过期机制
- 请求限流保护

### 2.2 AI 服务集成组件

#### 设计
- 通用 AI 服务接口抽象
- 支持多种 AI 提供商（OpenAI、本地模型等）
- 实现请求缓存以优化性能
- 异步处理长时间运行的 AI 任务

#### 关键接口
- `/api/v1/ai/completion` - 文本补全
- `/api/v1/ai/chat` - 对话式交互
- `/api/v1/ai/embeddings` - 生成文本嵌入

#### 优化策略
- 实现令牌桶算法进行速率限制
- 分布式缓存减少重复请求
- 故障转移机制提高可靠性

### 2.3 Token 管理组件

#### 设计
- 用户级别的 API 使用限额
- Token 使用情况跟踪与分析
- 基于用量的计费支持

#### 数据模型
- 用户 Token 配额表
- Token 使用记录表
- 计费周期和账单表

### 2.4 文件存储组件

#### 设计
- Cloudflare R2 存储集成
- 文件元数据管理
- 安全的文件访问控制
- 文件处理管道（验证、压缩、转换）

#### 关键接口
- `/api/v1/files/upload` - 文件上传
- `/api/v1/files/{id}` - 获取文件信息
- `/api/v1/files/{id}/download` - 文件下载

#### 安全考虑
- 文件类型验证
- 大小限制
- 内容扫描
- 签名 URL 限时访问

## 3. 数据库设计

### 3.1 实体关系图

```
+---------------+       +----------------+
| Users         |       | Tokens         |
+---------------+       +----------------+
| id (PK)       |<----->| id (PK)        |
| username      |       | user_id (FK)   |
| password_hash |       | token_value    |
| email         |       | expires_at     |
| role          |       | created_at     |
| created_at    |       +----------------+
| updated_at    |               |
+---------------+               |
        |                       |
        |                       |
        v                       v
+----------------+      +----------------+
| ApiKeys        |      | UsageRecords   |
+----------------+      +----------------+
| id (PK)        |      | id (PK)        |
| user_id (FK)   |      | token_id (FK)  |
| key_value      |      | endpoint       |
| name           |      | tokens_used    |
| enabled        |      | timestamp      |
| created_at     |      | cost           |
+----------------+      +----------------+
        |
        |
        v
+----------------+      +----------------+
| Files          |      | AiModels       |
+----------------+      +----------------+
| id (PK)        |      | id (PK)        |
| user_id (FK)   |      | name           |
| filename       |      | provider       |
| content_type   |      | description    |
| size           |      | config         |
| r2_object_key  |      | enabled        |
| created_at     |      | created_at     |
+----------------+      +----------------+
```

### 3.2 主要表结构

#### Users 表
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    role VARCHAR(20) NOT NULL DEFAULT 'user',
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
```

#### Tokens 表
```sql
CREATE TABLE tokens (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    token_value VARCHAR(100) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW()
);
```

#### UsageRecords 表
```sql
CREATE TABLE usage_records (
    id SERIAL PRIMARY KEY,
    token_id INTEGER REFERENCES tokens(id) ON DELETE SET NULL,
    endpoint VARCHAR(100) NOT NULL,
    tokens_used INTEGER NOT NULL,
    timestamp TIMESTAMP NOT NULL DEFAULT NOW(),
    cost DECIMAL(10, 6) NOT NULL DEFAULT 0
);
```

#### Files 表
```sql
CREATE TABLE files (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    filename VARCHAR(255) NOT NULL,
    content_type VARCHAR(100) NOT NULL,
    size BIGINT NOT NULL,
    r2_object_key VARCHAR(255) UNIQUE NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW()
);
```

## 4. 技术栈详情

### 4.1 后端技术

- **编程语言**: Go 1.21+
- **Web 框架**: Gin 1.9+
- **ORM**: GORM 或 SQLx
- **数据库**: PostgreSQL 15+
- **缓存**: Redis
- **认证**: JWT, OAuth2
- **文档**: Swagger/OpenAPI 3.0

### 4.2 存储技术

- **关系型数据库**: PostgreSQL
- **对象存储**: Cloudflare R2
- **缓存系统**: Redis

### 4.3 AI 集成

- **API 客户端**: OpenAI Go 客户端
- **模型管理**: 自定义 AI 管理服务
- **向量存储**: PostgreSQL + pgvector 扩展

### 4.4 部署技术

- **容器化**: Docker
- **容器编排**: Docker Compose (开发), Kubernetes (可选生产)
- **CI/CD**: GitHub Actions
- **监控**: Prometheus + Grafana

## 5. 接口设计

### 5.1 API 端点概览

| 端点 | 方法 | 描述 | 认证要求 |
|------|------|------|----------|
| `/api/v1/auth/register` | POST | 注册新用户 | 无 |
| `/api/v1/auth/login` | POST | 用户登录 | 无 |
| `/api/v1/auth/refresh` | POST | 刷新访问令牌 | 刷新令牌 |
| `/api/v1/auth/logout` | POST | 用户登出 | 访问令牌 |
| `/api/v1/users/me` | GET | 获取当前用户信息 | 访问令牌 |
| `/api/v1/users/me` | PUT | 更新用户信息 | 访问令牌 |
| `/api/v1/ai/completion` | POST | 获取 AI 补全 | 访问令牌 |
| `/api/v1/ai/chat` | POST | 聊天补全 | 访问令牌 |
| `/api/v1/ai/embeddings` | POST | 获取文本嵌入 | 访问令牌 |
| `/api/v1/files/upload` | POST | 上传文件 | 访问令牌 |
| `/api/v1/files` | GET | 列出用户文件 | 访问令牌 |
| `/api/v1/files/{id}` | GET | 获取文件信息 | 访问令牌 |
| `/api/v1/files/{id}/download` | GET | 下载文件 | 访问令牌 |
| `/api/v1/files/{id}` | DELETE | 删除文件 | 访问令牌 |
| `/api/v1/apikeys` | POST | 创建新 API 密钥 | 访问令牌 |
| `/api/v1/apikeys` | GET | 列出 API 密钥 | 访问令牌 |
| `/api/v1/apikeys/{id}` | DELETE | 删除 API 密钥 | 访问令牌 |
| `/api/v1/usage` | GET | 获取使用统计 | 访问令牌 |

### 5.2 API 请求/响应示例

#### 用户登录
**请求**:
```json
POST /api/v1/auth/login
{
    "username": "testuser",
    "password": "securepassword123"
}
```

**响应**:
```json
{
    "status": "success",
    "data": {
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "expires_in": 3600,
        "token_type": "Bearer"
    }
}
```

#### AI 聊天补全
**请求**:
```json
POST /api/v1/ai/chat
{
    "messages": [
        {"role": "user", "content": "你好，请介绍一下自己。"}
    ],
    "model": "gpt-3.5-turbo",
    "temperature": 0.7
}
```

**响应**:
```json
{
    "status": "success",
    "data": {
        "id": "chat-12345",
        "object": "chat.completion",
        "created": 1700000000,
        "model": "gpt-3.5-turbo",
        "choices": [
            {
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": "你好！我是一个AI助手，由OpenAI开发的GPT模型驱动..."
                },
                "finish_reason": "stop"
            }
        ],
        "usage": {
            "prompt_tokens": 11,
            "completion_tokens": 24,
            "total_tokens": 35
        }
    }
}
```

## 6. 安全架构

### 6.1 认证和授权

- JWT 基于 RS256 算法，使用公钥/私钥对
- 访问令牌短期有效（1小时）
- 刷新令牌长期有效（7天）但单次使用
- 基于角色的权限控制
- OAuth2 供第三方应用集成

### 6.2 API 安全

- 所有通信通过 HTTPS 加密
- API 密钥轮换机制
- 请求速率限制防止滥用
- 针对常见攻击向量的防护（CSRF、注入等）

### 6.3 数据安全

- 个人身份信息加密存储
- 数据库访问凭证安全管理
- 最小权限原则
- 敏感信息审计日志

## 7. 扩展性设计

### 7.1 水平扩展

- 无状态 API 服务支持多实例部署
- 数据库读写分离
- 缓存集群支持

### 7.2 模块化设计

- 服务组件可独立扩展
- 松耦合架构
- 插件系统支持新功能扩展

### 7.3 版本控制

- API 版本路径前缀 (/api/v1/...)
- 向后兼容性策略
- 平滑迁移计划