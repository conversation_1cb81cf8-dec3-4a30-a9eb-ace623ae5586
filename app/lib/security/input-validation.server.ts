/**
 * Input Validation and Sanitization System
 * Comprehensive input validation, sanitization, and security checks
 */

import { z } from "zod";

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  sanitizedData?: any;
  threats?: ThreatDetection;
}

export interface ThreatDetection {
  sqlInjection: boolean;
  xssAttempt: boolean;
  pathTraversal: boolean;
  scriptInjection: boolean;
  commandInjection: boolean;
  ldapInjection: boolean;
  xmlInjection: boolean;
}

export interface SanitizationOptions {
  allowHtml?: boolean;
  allowScripts?: boolean;
  maxLength?: number;
  trimWhitespace?: boolean;
  removeNullBytes?: boolean;
  normalizeUnicode?: boolean;
}

/**
 * Common validation schemas
 */
export const ValidationSchemas = {
  // User input schemas
  email: z.string().email().max(255),
  password: z.string().min(8).max(128),
  name: z
    .string()
    .min(1)
    .max(100)
    .regex(/^[a-zA-Z\s\-'.]+$/),
  username: z
    .string()
    .min(3)
    .max(30)
    .regex(/^[a-zA-Z0-9_-]+$/),

  // Content schemas
  title: z.string().min(1).max(200),
  description: z.string().max(2000),
  content: z.string().max(10000),
  comment: z.string().min(1).max(1000),

  // Technical schemas
  url: z.string().url().max(2048),
  uuid: z.string().uuid(),
  slug: z.string().regex(/^[a-z0-9-]+$/),

  // Numeric schemas
  positiveInt: z.number().int().positive(),
  rating: z.number().int().min(1).max(5),
  price: z.number().positive().max(999999.99),

  // API schemas
  apiKey: z
    .string()
    .min(32)
    .max(128)
    .regex(/^[a-zA-Z0-9_-]+$/),
  token: z.string().min(10).max(512),

  // File schemas
  filename: z
    .string()
    .min(1)
    .max(255)
    .regex(/^[a-zA-Z0-9._-]+$/),
  mimeType: z.string().regex(/^[a-zA-Z0-9][a-zA-Z0-9!#$&\-^_]*\/[a-zA-Z0-9][a-zA-Z0-9!#$&\-^_.]*$/),
};

/**
 * Detect security threats in input
 */
export function detectThreats(input: string): ThreatDetection {
  const normalizedInput = input.toLowerCase();

  return {
    sqlInjection: detectSQLInjection(normalizedInput),
    xssAttempt: detectXSS(input),
    pathTraversal: detectPathTraversal(input),
    scriptInjection: detectScriptInjection(normalizedInput),
    commandInjection: detectCommandInjection(normalizedInput),
    ldapInjection: detectLDAPInjection(normalizedInput),
    xmlInjection: detectXMLInjection(input),
  };
}

/**
 * Detect SQL injection patterns
 */
function detectSQLInjection(input: string): boolean {
  const sqlPatterns = [
    // Basic SQL keywords
    /\b(select|insert|update|delete|drop|create|alter|exec|execute|union|script)\b/i,

    // SQL operators and functions
    /\b(or|and)\s+\d+\s*=\s*\d+/i,
    /\b(or|and)\s+['"]?\w+['"]?\s*=\s*['"]?\w+['"]?/i,

    // SQL injection patterns
    /['"]?\s*(or|and)\s+['"]?1['"]?\s*=\s*['"]?1['"]?/i,
    /union\s+(all\s+)?select/i,
    /exec\s*\(/i,
    /sp_\w+/i,
    /xp_\w+/i,

    // Comment patterns
    /--[^\r\n]*/,
    /\/\*[\s\S]*?\*\//,

    // Hex and char functions
    /0x[0-9a-f]+/i,
    /char\s*\(/i,
    /ascii\s*\(/i,

    // Database-specific patterns
    /information_schema/i,
    /sysobjects/i,
    /syscolumns/i,
  ];

  return sqlPatterns.some((pattern) => pattern.test(input));
}

/**
 * Detect XSS patterns
 */
function detectXSS(input: string): boolean {
  const xssPatterns = [
    // Script tags
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,

    // Event handlers
    /on\w+\s*=\s*["'][^"']*["']/gi,
    /on\w+\s*=\s*[^"'\s>]+/gi,

    // JavaScript URLs
    /javascript\s*:/gi,
    /vbscript\s*:/gi,
    /data\s*:\s*text\/html/gi,

    // Dangerous tags
    /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
    /<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi,
    /<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi,
    /<applet\b[^<]*(?:(?!<\/applet>)<[^<]*)*<\/applet>/gi,
    /<meta\b[^>]*>/gi,
    /<link\b[^>]*>/gi,

    // Expression and eval
    /expression\s*\(/gi,
    /eval\s*\(/gi,
    /setTimeout\s*\(/gi,
    /setInterval\s*\(/gi,

    // Data URLs with scripts
    /data\s*:\s*[^,]*script/gi,
  ];

  return xssPatterns.some((pattern) => pattern.test(input));
}

/**
 * Detect path traversal patterns
 */
function detectPathTraversal(input: string): boolean {
  const traversalPatterns = [
    /\.\.\//g,
    /\.\.\\/g,
    /%2e%2e%2f/gi,
    /%2e%2e%5c/gi,
    /\.\.%2f/gi,
    /\.\.%5c/gi,
    /\.\.\\/gi,
    /\.\.\/\.\.\//gi,
  ];

  return traversalPatterns.some((pattern) => pattern.test(input));
}

/**
 * Detect script injection patterns
 */
function detectScriptInjection(input: string): boolean {
  const scriptPatterns = [
    /document\./gi,
    /window\./gi,
    /alert\s*\(/gi,
    /confirm\s*\(/gi,
    /prompt\s*\(/gi,
    /console\./gi,
    /location\./gi,
    /history\./gi,
  ];

  return scriptPatterns.some((pattern) => pattern.test(input));
}

/**
 * Detect command injection patterns
 */
function detectCommandInjection(input: string): boolean {
  const commandPatterns = [
    /;\s*(ls|dir|cat|type|more|less|head|tail|grep|find|ps|kill|rm|del|mv|cp|copy|mkdir|rmdir|chmod|chown)/i,
    /\|\s*(ls|dir|cat|type|more|less|head|tail|grep|find|ps|kill|rm|del|mv|cp|copy|mkdir|rmdir|chmod|chown)/i,
    /&&\s*(ls|dir|cat|type|more|less|head|tail|grep|find|ps|kill|rm|del|mv|cp|copy|mkdir|rmdir|chmod|chown)/i,
    /\$\(.*\)/,
    /`.*`/,
    /\${.*}/,
  ];

  return commandPatterns.some((pattern) => pattern.test(input));
}

/**
 * Detect LDAP injection patterns
 */
function detectLDAPInjection(input: string): boolean {
  const ldapPatterns = [/\(\s*\|\s*\(/, /\(\s*&\s*\(/, /\(\s*!\s*\(/, /\*\s*\)/, /\(\s*\*/];

  return ldapPatterns.some((pattern) => pattern.test(input));
}

/**
 * Detect XML injection patterns
 */
function detectXMLInjection(input: string): boolean {
  const xmlPatterns = [/<!\[CDATA\[/gi, /<!DOCTYPE/gi, /<!ENTITY/gi, /<\?xml/gi, /&\w+;/g];

  return xmlPatterns.some((pattern) => pattern.test(input));
}

/**
 * Sanitize string input
 */
export function sanitizeString(input: string, options: SanitizationOptions = {}): string {
  let sanitized = input;

  // Remove null bytes
  if (options.removeNullBytes !== false) {
    sanitized = sanitized.replace(/\0/g, "");
  }

  // Normalize Unicode
  if (options.normalizeUnicode) {
    sanitized = sanitized.normalize("NFC");
  }

  // Trim whitespace
  if (options.trimWhitespace !== false) {
    sanitized = sanitized.trim();
  }

  // Remove or escape HTML
  if (!options.allowHtml) {
    sanitized = escapeHtml(sanitized);
  }

  // Remove scripts
  if (!options.allowScripts) {
    sanitized = removeScripts(sanitized);
  }

  // Enforce max length
  if (options.maxLength && sanitized.length > options.maxLength) {
    sanitized = sanitized.substring(0, options.maxLength);
  }

  return sanitized;
}

/**
 * Escape HTML characters
 */
function escapeHtml(input: string): string {
  const htmlEscapes: Record<string, string> = {
    "&": "&amp;",
    "<": "&lt;",
    ">": "&gt;",
    '"': "&quot;",
    "'": "&#x27;",
    "/": "&#x2F;",
  };

  return input.replace(/[&<>"'/]/g, (match) => htmlEscapes[match]);
}

/**
 * Remove script tags and dangerous content
 */
function removeScripts(input: string): string {
  let sanitized = input;

  // Remove script tags
  sanitized = sanitized.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, "");

  // Remove event handlers
  sanitized = sanitized.replace(/on\w+\s*=\s*["'][^"']*["']/gi, "");
  sanitized = sanitized.replace(/on\w+\s*=\s*[^"'\s>]+/gi, "");

  // Remove javascript: URLs
  sanitized = sanitized.replace(/javascript\s*:/gi, "");

  return sanitized;
}

/**
 * Validate and sanitize object using Zod schema
 */
export function validateAndSanitize<T>(
  data: unknown,
  schema: z.ZodSchema<T>,
  sanitizationOptions?: SanitizationOptions
): ValidationResult {
  try {
    // First, sanitize string fields if data is an object
    let sanitizedData = data;
    if (typeof data === "object" && data !== null && sanitizationOptions) {
      sanitizedData = sanitizeObject(data, sanitizationOptions);
    }

    // Validate with schema
    const result = schema.safeParse(sanitizedData);

    if (result.success) {
      // Check for threats in string values
      const threats = checkObjectForThreats(result.data);
      const hasThreats = Object.values(threats).some(Boolean);

      return {
        isValid: !hasThreats,
        errors: hasThreats ? ["Security threats detected in input"] : [],
        sanitizedData: result.data,
        threats,
      };
    } else {
      return {
        isValid: false,
        errors: result.error.errors.map((err) => `${err.path.join(".")}: ${err.message}`),
      };
    }
  } catch (error) {
    return {
      isValid: false,
      errors: ["Validation failed: " + (error instanceof Error ? error.message : "Unknown error")],
    };
  }
}

/**
 * Sanitize all string properties in an object
 */
function sanitizeObject(obj: any, options: SanitizationOptions): any {
  if (typeof obj === "string") {
    return sanitizeString(obj, options);
  }

  if (Array.isArray(obj)) {
    return obj.map((item) => sanitizeObject(item, options));
  }

  if (typeof obj === "object" && obj !== null) {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(obj)) {
      sanitized[key] = sanitizeObject(value, options);
    }
    return sanitized;
  }

  return obj;
}

/**
 * Check object for security threats
 */
function checkObjectForThreats(obj: any): ThreatDetection {
  const threats: ThreatDetection = {
    sqlInjection: false,
    xssAttempt: false,
    pathTraversal: false,
    scriptInjection: false,
    commandInjection: false,
    ldapInjection: false,
    xmlInjection: false,
  };

  function checkValue(value: any): void {
    if (typeof value === "string") {
      const valueThreats = detectThreats(value);
      Object.keys(threats).forEach((key) => {
        if (valueThreats[key as keyof ThreatDetection]) {
          threats[key as keyof ThreatDetection] = true;
        }
      });
    } else if (Array.isArray(value)) {
      value.forEach(checkValue);
    } else if (typeof value === "object" && value !== null) {
      Object.values(value).forEach(checkValue);
    }
  }

  checkValue(obj);
  return threats;
}

/**
 * Create validation middleware
 */
export function createValidationMiddleware<T>(
  schema: z.ZodSchema<T>,
  sanitizationOptions?: SanitizationOptions
) {
  return async (data: unknown): Promise<ValidationResult> => {
    return validateAndSanitize(data, schema, sanitizationOptions);
  };
}

/**
 * Common validation middleware instances
 */
export const validators = {
  email: createValidationMiddleware(ValidationSchemas.email),
  password: createValidationMiddleware(ValidationSchemas.password),
  name: createValidationMiddleware(ValidationSchemas.name),
  username: createValidationMiddleware(ValidationSchemas.username),
  title: createValidationMiddleware(ValidationSchemas.title),
  description: createValidationMiddleware(ValidationSchemas.description),
  content: createValidationMiddleware(ValidationSchemas.content),
  url: createValidationMiddleware(ValidationSchemas.url),
  uuid: createValidationMiddleware(ValidationSchemas.uuid),
  apiKey: createValidationMiddleware(ValidationSchemas.apiKey),
};
