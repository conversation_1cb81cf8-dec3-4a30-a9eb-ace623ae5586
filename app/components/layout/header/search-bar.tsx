import { Search } from "lucide-react";
import { useState } from "react";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { cn } from "~/lib/utils/utils";
import type { SearchProps } from "./types";

export function SearchBar({
  placeholder = "Search...",
  onSearch,
  showSearchButton = true,
  className,
}: SearchProps) {
  const [searchQuery, setSearchQuery] = useState("");

  const handleSearch = () => {
    if (onSearch && searchQuery.trim()) {
      onSearch(searchQuery.trim());
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  return (
    <div className={cn("relative flex items-center max-w-sm", className)}>
      <Input
        type="search"
        placeholder={placeholder}
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
        onKeyDown={handleKeyDown}
        className="pr-10 bg-muted/50 border-muted-foreground/20 focus:border-primary"
        aria-label="Search"
      />
      {showSearchButton && (
        <Button
          size="sm"
          variant="ghost"
          onClick={handleSearch}
          className="absolute right-1 h-7 w-7 p-0 hover:bg-primary hover:text-primary-foreground"
          aria-label="Search button"
        >
          <Search className="h-4 w-4" />
        </Button>
      )}
    </div>
  );
}
